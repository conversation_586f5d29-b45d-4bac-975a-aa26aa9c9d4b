#!/bin/bash

# Database Migration Script
# This script runs the single merged migration for database setup

set -e

# Default values
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="password"
DB_NAME="notification_service"
SSL_MODE="disable"
MIGRATIONS_TABLE="schema_migrations"  # Default migration table name

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            DB_HOST="$2"
            shift 2
            ;;
        --port)
            DB_PORT="$2"
            shift 2
            ;;
        --user)
            DB_USER="$2"
            shift 2
            ;;
        --password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        --database)
            DB_NAME="$2"
            shift 2
            ;;
        --ssl-mode)
            SSL_MODE="$2"
            shift 2
            ;;
        --migrations-table)
            MIGRATIONS_TABLE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --host HOST             Database host (default: localhost)"
            echo "  --port PORT             Database port (default: 5432)"
            echo "  --user USER             Database user (default: postgres)"
            echo "  --password PASS         Database password (default: password)"
            echo "  --database DB           Database name (default: notification_service)"
            echo "  --ssl-mode MODE         SSL mode (default: disable)"
            echo "  --migrations-table TBL  Migration table name (default: schema_migrations)"
            echo "  --help                  Show this help message"
            echo ""
            echo "Example:"
            echo "  $0 --host localhost --user myuser --password mypass --database mydb"
            echo "  $0 --migrations-table my_custom_migrations"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Build connection string with custom migration table
CONNECTION_STRING="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=${SSL_MODE}&x-migrations-table=${MIGRATIONS_TABLE}"

echo "🚀 Running database migration..."
echo "📍 Host: ${DB_HOST}:${DB_PORT}"
echo "🗄️  Database: ${DB_NAME}"
echo "👤 User: ${DB_USER}"
echo "📋 Migration table: ${MIGRATIONS_TABLE}"
echo ""

# Check if migrate command exists
if ! command -v migrate &> /dev/null; then
    echo "❌ Error: 'migrate' command not found"
    echo "Please install golang-migrate:"
    echo "  - macOS: brew install golang-migrate"
    echo "  - Linux: See https://github.com/golang-migrate/migrate/tree/master/cmd/migrate"
    exit 1
fi

# Run the single migration
echo "🔄 Running migration..."
if migrate -path migrations -database "${CONNECTION_STRING}" up 1; then
    echo "✅ Migration completed successfully!"
    echo ""
    echo "📋 Tables created:"
    echo "  - notification_banks (main notifications table)"
    echo "  - fcm_tokens (FCM tokens for push notifications)"
    echo "  - notification_types (notification type definitions)"
    echo "  - notification_categories (notification category definitions)"
    echo ""
    echo "🎉 Database is ready for use!"
    echo "📊 Migration tracking table: ${MIGRATIONS_TABLE}"
else
    echo "❌ Migration failed!"
    exit 1
fi

# Database Migration Script
# This script runs the single merged migration for database setup

set -e

# Default values
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="password"
DB_NAME="notification_service"
SSL_MODE="disable"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            DB_HOST="$2"
            shift 2
            ;;
        --port)
            DB_PORT="$2"
            shift 2
            ;;
        --user)
            DB_USER="$2"
            shift 2
            ;;
        --password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        --database)
            DB_NAME="$2"
            shift 2
            ;;
        --ssl-mode)
            SSL_MODE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --host HOST         Database host (default: localhost)"
            echo "  --port PORT         Database port (default: 5432)"
            echo "  --user USER         Database user (default: postgres)"
            echo "  --password PASS     Database password (default: password)"
            echo "  --database DB       Database name (default: notification_service)"
            echo "  --ssl-mode MODE     SSL mode (default: disable)"
            echo "  --help              Show this help message"
            echo ""
            echo "Example:"
            echo "  $0 --host localhost --user myuser --password mypass --database mydb"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Build connection string
CONNECTION_STRING="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=${SSL_MODE}"

echo "🚀 Running database migration..."
echo "📍 Host: ${DB_HOST}:${DB_PORT}"
echo "🗄️  Database: ${DB_NAME}"
echo "👤 User: ${DB_USER}"
echo ""

# Check if migrate command exists
if ! command -v migrate &> /dev/null; then
    echo "❌ Error: 'migrate' command not found"
    echo "Please install golang-migrate:"
    echo "  - macOS: brew install golang-migrate"
    echo "  - Linux: See https://github.com/golang-migrate/migrate/tree/master/cmd/migrate"
    exit 1
fi

# Run the single migration
echo "🔄 Running migration..."
if migrate -path migrations -database "${CONNECTION_STRING}" up 1; then
    echo "✅ Migration completed successfully!"
    echo ""
    echo "📋 Tables created:"
    echo "  - notification_banks (main notifications table)"
    echo "  - fcm_tokens (FCM tokens for push notifications)"
    echo "  - notification_types (notification type definitions)"
    echo "  - notification_categories (notification category definitions)"
    echo ""
    echo "🎉 Database is ready for use!"
else
    echo "❌ Migration failed!"
    exit 1
fi
