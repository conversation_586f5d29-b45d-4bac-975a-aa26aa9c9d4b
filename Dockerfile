# Use official Golang image as a base
FROM golang:1.24.4-alpine as builder

RUN apk add --no-cache git make gcc musl-dev openssh-client

# Set the current working directory inside the container
WORKDIR /app

# Set up SSH & git configuration (only if private repos are needed)
ARG SSH_KEY
ENV GOPRIVATE=bitbucket.org/mid-kelola-indonesia/go-utils

RUN mkdir -p ~/.ssh && chmod 700 ~/.ssh && \
    if [ -n "$SSH_KEY" ]; then \
        echo "$SSH_KEY" | base64 -d > ~/.ssh/id_rsa && \
        chmod 600 ~/.ssh/id_rsa && \
        ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts && \
        git config --global url."ssh://*****************/".insteadOf https://bitbucket.org/ ; \
    fi

# Copy the Go modules files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod tidy

# Copy the source code into the container
COPY . .

# Build the Go application
RUN go build -o app .

# Create a new stage to keep the image small
FROM alpine:latest

# Install necessary dependencies
RUN apk --no-cache add ca-certificates

# Create a non-root user
RUN adduser -D -g '' appuser

# Set the working directory
WORKDIR /app

# Copy the built Go binary from the builder stage
COPY --from=builder /app/app .
COPY --from=builder /app/certs ./certs

# Set the ownership of the application binary to the non-root user
RUN chown appuser:appuser ./app

# Switch to the non-root user
USER appuser

# Expose the port your app will run on
EXPOSE 4000

# Run the Go application
CMD ["./app", "server"]
