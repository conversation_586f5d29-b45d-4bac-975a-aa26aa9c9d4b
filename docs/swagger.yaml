definitions:
  domains.ErrorDetail:
    description: Error detail structure
    properties:
      error:
        description: Error message
        example: Invalid input
        type: string
    type: object
  domains.PaginationMeta:
    description: Pagination metadata structure
    properties:
      limit:
        description: Number of items per page
        example: 10
        type: integer
      page:
        description: Current page number
        example: 1
        type: integer
      total:
        description: Total number of items
        example: 100
        type: integer
      total_page:
        description: Total number of pages
        example: 10
        type: integer
    type: object
  domains.Response:
    description: Standard API response structure for both success and error cases
    properties:
      data:
        description: Data contains the response payload for successful requests
        type: object
      errors:
        description: Errors contains error details when the request fails
        items:
          type: object
        type: array
      meta:
        description: Meta contains metadata about the response (pagination info, messages,
          etc.)
        type: object
    type: object
  handler.AppCreateNotificationRequest:
    properties:
      click_action:
        allOf:
        - $ref: '#/definitions/model.ClickAction'
        example: OPEN_URL
      click_action_url:
        example: https://example.com/message/123
        type: string
      description:
        example: You have a new message
        type: string
      event_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      event_type:
        example: add_agent
        type: string
      extra:
        additionalProperties: true
        type: object
      is_reminder:
        example: false
        type: boolean
      notif_category:
        example: category_123
        type: string
      notif_type:
        example: type_123
        type: string
      organization_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      sso_id:
        example: 493fc4e6-14b9-4633-95d0-86b2e27c2387
        type: string
      title:
        example: New Message
        type: string
    required:
    - description
    - title
    type: object
  handler.CountUnreadResponse:
    properties:
      count:
        example: 5
        type: integer
    type: object
  handler.CreateNotificationRequest:
    properties:
      click_action:
        allOf:
        - $ref: '#/definitions/model.ClickAction'
        example: OPEN_URL
      click_action_url:
        example: https://example.com/message/123
        type: string
      description:
        example: You have a new message
        type: string
      event_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      event_type:
        example: add_agent
        type: string
      extra:
        additionalProperties: true
        type: object
      is_reminder:
        example: false
        type: boolean
      notif_category:
        example: category_123
        type: string
      notif_type:
        example: type_123
        type: string
      organization_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      origin:
        example: chat
        type: string
      sso_id:
        example: 493fc4e6-14b9-4633-95d0-86b2e27c2387
        type: string
      title:
        example: New Message
        type: string
    required:
    - description
    - title
    type: object
  handler.RegisterTokenRequest:
    properties:
      sso_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      token:
        example: fcm_token_string
        type: string
      user_source:
        allOf:
        - $ref: '#/definitions/model.UserSource'
        example: mobile_app
      user_type:
        allOf:
        - $ref: '#/definitions/model.UserType'
        example: customer
    required:
    - sso_id
    - token
    - user_source
    - user_type
    type: object
  model.ClickAction:
    enum:
    - OPEN_URL
    - OPEN_APP
    type: string
    x-enum-varnames:
    - ClickActionOpenURL
    - ClickActionOpenApp
  model.FCMToken:
    properties:
      created_at:
        type: string
      id:
        type: string
      sso_id:
        type: string
      token:
        type: string
      updated_at:
        type: string
      user_source:
        $ref: '#/definitions/model.UserSource'
      user_type:
        $ref: '#/definitions/model.UserType'
    type: object
  model.UserSource:
    enum:
    - crm
    - chat
    - notification_service
    type: string
    x-enum-varnames:
    - UserSourceCRM
    - UserSourceChat
    - UserSourceNotificationSvc
  model.UserType:
    enum:
    - admin
    - supervisor
    - agent
    type: string
    x-enum-varnames:
    - UserTypeAdmin
    - UserTypeSupervisor
    - UserTypeAgent
  service.NotificationView:
    properties:
      click_action:
        allOf:
        - $ref: '#/definitions/model.ClickAction'
        example: OPEN_NEW_CHAT
      click_action_url:
        example: https://staging-chat.qontak.com/inbox/123
        type: string
      created_at:
        example: "2025-08-04T06:23:15+07:00"
        type: string
      description:
        example: Supervisor has assigned you to a Chat
        type: string
      event_id:
        example: 123e4567-e89b-12d3-a456-426614174222
        type: string
      event_type:
        example: EVENT_NAME
        type: string
      extra: {}
      id:
        example: f77f7e12-a72f-4e67-b359-512c7e5415bc
        type: string
      is_reminder:
        example: false
        type: boolean
      notif_category:
        example: "1"
        type: string
      notif_type:
        example: "3"
        type: string
      organization_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      origin:
        example: chat
        type: string
      read_at:
        example: "2025-08-04T18:55:41+07:00"
        type: string
      title:
        example: New Chat Assignment
        type: string
    type: object
info:
  contact: {}
paths:
  /api/v1/fcm-tokens:
    post:
      consumes:
      - application/json
      description: Register a new Firebase Cloud Messaging token for a user
      parameters:
      - description: FCM token registration details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.RegisterTokenRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Token registered successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/model.FCMToken'
              type: object
        "400":
          description: Invalid request body or missing required fields
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Register FCM token
      tags:
      - fcm-tokens
  /api/v1/fcm-tokens/{id}:
    delete:
      description: Delete a specific FCM token by its ID
      parameters:
      - description: Token ID (UUID)
        format: uuid
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Token deleted successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                message:
                  type: string
              type: object
        "400":
          description: Invalid token ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "404":
          description: Token not found
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Delete FCM token
      tags:
      - fcm-tokens
  /api/v1/fcm-tokens/sso/{sso_id}:
    get:
      description: Retrieve all FCM tokens associated with a specific SSO ID
      parameters:
      - description: SSO ID (UUID)
        format: uuid
        in: path
        name: sso_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of FCM tokens
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/model.FCMToken'
                  type: array
              type: object
        "400":
          description: Invalid SSO ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Get FCM tokens by SSO ID
      tags:
      - fcm-tokens
  /api/v1/health:
    get:
      description: Check if the service is healthy and running
      produces:
      - application/json
      responses:
        "200":
          description: Service is healthy
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                message:
                  type: string
              type: object
      summary: Health check
      tags:
      - health
  /api/v1/notifications:
    get:
      description: Retrieve a paginated list of notifications for the authenticated
        user
      parameters:
      - description: User SSO ID
        in: header
        name: X-Authenticated-Userid
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        minimum: 1
        name: page
        type: integer
      - description: 'Number of items per page (default: 10, max: 100)'
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - description: Notification origin app
        example: crm/chat
        in: query
        name: origin
        type: string
      - description: Notification read/unread status
        example: read/unread
        in: query
        name: status
        type: string
      - description: Type of notification (per origin app)
        example: type_1
        in: query
        name: notif_type
        type: string
      - description: Category of notification (per origin app)
        example: category_1
        in: query
        name: notif_category
        type: string
      - description: Time offset (in hour) based on your timezone
        in: query
        maximum: 14
        minimum: -14
        name: time_offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of notifications with pagination metadata
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/service.NotificationView'
                  type: array
                meta:
                  $ref: '#/definitions/domains.PaginationMeta'
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Get all notifications
      tags:
      - notifications
    post:
      consumes:
      - application/json
      deprecated: true
      description: Create a new notification for a specific user
      parameters:
      - description: Notification details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.CreateNotificationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Notification created successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/service.NotificationView'
              type: object
        "400":
          description: Invalid request body
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Create a new notification
      tags:
      - notifications
  /api/v1/notifications/{id}:
    get:
      description: Retrieve a specific notification by its ID (per user)
      parameters:
      - description: User SSO ID
        in: header
        name: X-Authenticated-Userid
        required: true
        type: string
      - description: Notification ID (UUID)
        format: uuid
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Notification details
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/service.NotificationView'
              type: object
        "400":
          description: Invalid notification ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "404":
          description: Notification not found
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Get notification by ID
      tags:
      - notifications
  /api/v1/notifications/chat:
    post:
      consumes:
      - application/json
      description: Create a new notification for a specific user
      parameters:
      - description: Internal Notification Service API Key
        in: header
        name: X-Api-Key
        required: true
        type: string
      - description: Notification details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.AppCreateNotificationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Notification created successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/service.NotificationView'
              type: object
        "400":
          description: Invalid request body
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Create a new notification (for Chat app)
      tags:
      - notifications
  /api/v1/notifications/crm:
    post:
      consumes:
      - application/json
      description: Create a new notification for a specific user
      parameters:
      - description: Internal Notification Service API Key
        in: header
        name: X-Api-Key
        required: true
        type: string
      - description: Notification details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.AppCreateNotificationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Notification created successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/service.NotificationView'
              type: object
        "400":
          description: Invalid request body
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Create a new notification (for CRM app)
      tags:
      - notifications
  /api/v1/notifications/mark_all_as_read:
    put:
      description: Mark all notifications as read for the authenticated user
      parameters:
      - description: User SSO ID
        in: header
        name: X-Authenticated-Userid
        required: true
        type: string
      - description: Type of notification (per origin app)
        example: type_1
        in: query
        name: notif_type
        type: string
      - description: Time offset (in hour) based on your timezone
        in: query
        maximum: 14
        minimum: -14
        name: time_offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: All notifications marked as read successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                message:
                  type: string
              type: object
        "400":
          description: Invalid SSO ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Mark all notifications as read
      tags:
      - notifications
  /api/v1/notifications/mark_as_read/{id}:
    put:
      description: Mark a specific notification as read
      parameters:
      - description: User SSO ID
        in: header
        name: X-Authenticated-Userid
        required: true
        type: string
      - description: Notification ID (UUID)
        format: uuid
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Notification marked as read successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                message:
                  type: string
              type: object
        "400":
          description: Invalid notification ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "404":
          description: Notification not found
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Mark notification as read
      tags:
      - notifications
  /api/v1/notifications/unread/count:
    get:
      description: Get the count of unread notifications for the authenticated user
      parameters:
      - description: User SSO ID
        in: header
        name: X-Authenticated-Userid
        required: true
        type: string
      - description: Type of notification (per origin app)
        example: type_1
        in: query
        name: notif_type
        type: string
      - description: Time offset (in hour) based on your timezone
        in: query
        maximum: 14
        minimum: -14
        name: time_offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Count of unread notifications
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/handler.CountUnreadResponse'
              type: object
        "400":
          description: Invalid SSO ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Count unread notifications
      tags:
      - notifications
swagger: "2.0"
