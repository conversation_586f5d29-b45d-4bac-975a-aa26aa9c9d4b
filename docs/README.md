# Notification Service Documentation

Welcome to the Notification Service documentation. This documentation provides comprehensive information about the service, its architecture, configuration, and usage.

## Table of Contents

### Development Guides

- [Development Guide](development/README.md) - Comprehensive guide for developers
- [Architecture](development/architecture.md) - Detailed architecture documentation
- [Configuration](development/configuration.md) - Configuration options and management
- [API Documentation](development/api.md) - API endpoints and usage
- [New Features](development/new_features.md) - Recent updates and new features
- [FCM Kafka Consumer](development/fcm_kafka_consumer.md) - FCM Kafka consumer documentation
- [Troubleshooting](development/troubleshooting.md) - Common issues and solutions

## Quick Links

- [Project Repository](https://bitbucket.org/terbang-ventures/notification-service)
- [Issue Tracker](https://bitbucket.org/terbang-ventures/notification-service/issues)

## Getting Started

To get started with the Notification Service, follow these steps:

1. Clone the repository
2. Set up the development environment as described in the [Development Guide](development/README.md)
3. Create a `.env` file using the [Configuration Guide](development/configuration.md)
4. Run the service and explore the API as described in the [API Documentation](development/api.md)

## Contributing

We welcome contributions to the Notification Service! Please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linters
5. Submit a pull request

For more details, see the [Contributing Guidelines](development/README.md#contributing-guidelines).

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Contact

For questions or support, please contact the development team at [<EMAIL>](mailto:<EMAIL>).
