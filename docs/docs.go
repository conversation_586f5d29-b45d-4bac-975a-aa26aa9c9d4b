// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/fcm-tokens": {
            "post": {
                "description": "Register a new Firebase Cloud Messaging token for a user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "fcm-tokens"
                ],
                "summary": "Register FCM token",
                "parameters": [
                    {
                        "description": "FCM token registration details",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handler.RegisterTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Token registered successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/model.FCMToken"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body or missing required fields",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/fcm-tokens/sso/{sso_id}": {
            "get": {
                "description": "Retrieve all FCM tokens associated with a specific SSO ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "fcm-tokens"
                ],
                "summary": "Get FCM tokens by SSO ID",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "SSO ID (UUID)",
                        "name": "sso_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of FCM tokens",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.FCMToken"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid SSO ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/fcm-tokens/{id}": {
            "delete": {
                "description": "Delete a specific FCM token by its ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "fcm-tokens"
                ],
                "summary": "Delete FCM token",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Token ID (UUID)",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Token deleted successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid token ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "Token not found",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/health": {
            "get": {
                "description": "Check if the service is healthy and running",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "Health check",
                "responses": {
                    "200": {
                        "description": "Service is healthy",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/notifications": {
            "get": {
                "description": "Retrieve a paginated list of notifications for the authenticated user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Get all notifications",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User SSO ID",
                        "name": "X-Authenticated-Userid",
                        "in": "header",
                        "required": true
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "description": "Number of items per page (default: 10, max: 100)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "crm/chat",
                        "description": "Notification origin app",
                        "name": "origin",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "read/unread",
                        "description": "Notification read/unread status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "type_1",
                        "description": "Type of notification (per origin app)",
                        "name": "notif_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "category_1",
                        "description": "Category of notification (per origin app)",
                        "name": "notif_category",
                        "in": "query"
                    },
                    {
                        "maximum": 14,
                        "minimum": -14,
                        "type": "integer",
                        "description": "Time offset (in hour) based on your timezone",
                        "name": "time_offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of notifications with pagination metadata",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/service.NotificationView"
                                            }
                                        },
                                        "meta": {
                                            "$ref": "#/definitions/domains.PaginationMeta"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "description": "Create a new notification for a specific user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Create a new notification",
                "deprecated": true,
                "parameters": [
                    {
                        "description": "Notification details",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handler.CreateNotificationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Notification created successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service.NotificationView"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/chat": {
            "post": {
                "description": "Create a new notification for a specific user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Create a new notification (for Chat app)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Internal Notification Service API Key",
                        "name": "X-Api-Key",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "Notification details",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handler.AppCreateNotificationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Notification created successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service.NotificationView"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/crm": {
            "post": {
                "description": "Create a new notification for a specific user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Create a new notification (for CRM app)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Internal Notification Service API Key",
                        "name": "X-Api-Key",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "Notification details",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handler.AppCreateNotificationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Notification created successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service.NotificationView"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/mark_all_as_read": {
            "put": {
                "description": "Mark all notifications as read for the authenticated user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Mark all notifications as read",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User SSO ID",
                        "name": "X-Authenticated-Userid",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "type_1",
                        "description": "Type of notification (per origin app)",
                        "name": "notif_type",
                        "in": "query"
                    },
                    {
                        "maximum": 14,
                        "minimum": -14,
                        "type": "integer",
                        "description": "Time offset (in hour) based on your timezone",
                        "name": "time_offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "All notifications marked as read successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid SSO ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/mark_as_read/{id}": {
            "put": {
                "description": "Mark a specific notification as read",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Mark notification as read",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User SSO ID",
                        "name": "X-Authenticated-Userid",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Notification ID (UUID)",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Notification marked as read successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid notification ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "Notification not found",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/unread/count": {
            "get": {
                "description": "Get the count of unread notifications for the authenticated user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Count unread notifications",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User SSO ID",
                        "name": "X-Authenticated-Userid",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "type_1",
                        "description": "Type of notification (per origin app)",
                        "name": "notif_type",
                        "in": "query"
                    },
                    {
                        "maximum": 14,
                        "minimum": -14,
                        "type": "integer",
                        "description": "Time offset (in hour) based on your timezone",
                        "name": "time_offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Count of unread notifications",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/handler.CountUnreadResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid SSO ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/{id}": {
            "get": {
                "description": "Retrieve a specific notification by its ID (per user)",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Get notification by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User SSO ID",
                        "name": "X-Authenticated-Userid",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Notification ID (UUID)",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Notification details",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service.NotificationView"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid notification ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "Notification not found",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/domains.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "errors": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/domains.ErrorDetail"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "domains.ErrorDetail": {
            "description": "Error detail structure",
            "type": "object",
            "properties": {
                "error": {
                    "description": "Error message",
                    "type": "string",
                    "example": "Invalid input"
                }
            }
        },
        "domains.PaginationMeta": {
            "description": "Pagination metadata structure",
            "type": "object",
            "properties": {
                "limit": {
                    "description": "Number of items per page",
                    "type": "integer",
                    "example": 10
                },
                "page": {
                    "description": "Current page number",
                    "type": "integer",
                    "example": 1
                },
                "total": {
                    "description": "Total number of items",
                    "type": "integer",
                    "example": 100
                },
                "total_page": {
                    "description": "Total number of pages",
                    "type": "integer",
                    "example": 10
                }
            }
        },
        "domains.Response": {
            "description": "Standard API response structure for both success and error cases",
            "type": "object",
            "properties": {
                "data": {
                    "description": "Data contains the response payload for successful requests",
                    "type": "object"
                },
                "errors": {
                    "description": "Errors contains error details when the request fails",
                    "type": "array",
                    "items": {
                        "type": "object"
                    }
                },
                "meta": {
                    "description": "Meta contains metadata about the response (pagination info, messages, etc.)",
                    "type": "object"
                }
            }
        },
        "handler.AppCreateNotificationRequest": {
            "type": "object",
            "required": [
                "description",
                "title"
            ],
            "properties": {
                "click_action": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.ClickAction"
                        }
                    ],
                    "example": "OPEN_URL"
                },
                "click_action_url": {
                    "type": "string",
                    "example": "https://example.com/message/123"
                },
                "description": {
                    "type": "string",
                    "example": "You have a new message"
                },
                "event_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "event_type": {
                    "type": "string",
                    "example": "add_agent"
                },
                "extra": {
                    "type": "object",
                    "additionalProperties": true
                },
                "is_reminder": {
                    "type": "boolean",
                    "example": false
                },
                "notif_category": {
                    "type": "string",
                    "example": "category_123"
                },
                "notif_type": {
                    "type": "string",
                    "example": "type_123"
                },
                "organization_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "sso_id": {
                    "type": "string",
                    "example": "493fc4e6-14b9-4633-95d0-86b2e27c2387"
                },
                "title": {
                    "type": "string",
                    "example": "New Message"
                }
            }
        },
        "handler.CountUnreadResponse": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer",
                    "example": 5
                }
            }
        },
        "handler.CreateNotificationRequest": {
            "type": "object",
            "required": [
                "description",
                "title"
            ],
            "properties": {
                "click_action": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.ClickAction"
                        }
                    ],
                    "example": "OPEN_URL"
                },
                "click_action_url": {
                    "type": "string",
                    "example": "https://example.com/message/123"
                },
                "description": {
                    "type": "string",
                    "example": "You have a new message"
                },
                "event_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "event_type": {
                    "type": "string",
                    "example": "add_agent"
                },
                "extra": {
                    "type": "object",
                    "additionalProperties": true
                },
                "is_reminder": {
                    "type": "boolean",
                    "example": false
                },
                "notif_category": {
                    "type": "string",
                    "example": "category_123"
                },
                "notif_type": {
                    "type": "string",
                    "example": "type_123"
                },
                "organization_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "origin": {
                    "type": "string",
                    "example": "chat"
                },
                "sso_id": {
                    "type": "string",
                    "example": "493fc4e6-14b9-4633-95d0-86b2e27c2387"
                },
                "title": {
                    "type": "string",
                    "example": "New Message"
                }
            }
        },
        "handler.RegisterTokenRequest": {
            "type": "object",
            "required": [
                "sso_id",
                "token",
                "user_source",
                "user_type"
            ],
            "properties": {
                "sso_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "token": {
                    "type": "string",
                    "example": "fcm_token_string"
                },
                "user_source": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.UserSource"
                        }
                    ],
                    "example": "mobile_app"
                },
                "user_type": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.UserType"
                        }
                    ],
                    "example": "customer"
                }
            }
        },
        "model.ClickAction": {
            "type": "string",
            "enum": [
                "OPEN_URL",
                "OPEN_APP"
            ],
            "x-enum-varnames": [
                "ClickActionOpenURL",
                "ClickActionOpenApp"
            ]
        },
        "model.FCMToken": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "sso_id": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_source": {
                    "$ref": "#/definitions/model.UserSource"
                },
                "user_type": {
                    "$ref": "#/definitions/model.UserType"
                }
            }
        },
        "model.UserSource": {
            "type": "string",
            "enum": [
                "crm",
                "chat",
                "notification_service"
            ],
            "x-enum-varnames": [
                "UserSourceCRM",
                "UserSourceChat",
                "UserSourceNotificationSvc"
            ]
        },
        "model.UserType": {
            "type": "string",
            "enum": [
                "admin",
                "supervisor",
                "agent"
            ],
            "x-enum-varnames": [
                "UserTypeAdmin",
                "UserTypeSupervisor",
                "UserTypeAgent"
            ]
        },
        "service.NotificationView": {
            "type": "object",
            "properties": {
                "click_action": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.ClickAction"
                        }
                    ],
                    "example": "OPEN_NEW_CHAT"
                },
                "click_action_url": {
                    "type": "string",
                    "example": "https://staging-chat.qontak.com/inbox/123"
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-08-04T06:23:15+07:00"
                },
                "description": {
                    "type": "string",
                    "example": "Supervisor has assigned you to a Chat"
                },
                "event_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174222"
                },
                "event_type": {
                    "type": "string",
                    "example": "EVENT_NAME"
                },
                "extra": {},
                "id": {
                    "type": "string",
                    "example": "f77f7e12-a72f-4e67-b359-512c7e5415bc"
                },
                "is_reminder": {
                    "type": "boolean",
                    "example": false
                },
                "notif_category": {
                    "type": "string",
                    "example": "1"
                },
                "notif_type": {
                    "type": "string",
                    "example": "3"
                },
                "organization_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "origin": {
                    "type": "string",
                    "example": "chat"
                },
                "read_at": {
                    "type": "string",
                    "example": "2025-08-04T18:55:41+07:00"
                },
                "title": {
                    "type": "string",
                    "example": "New Chat Assignment"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
