# Automatic Notification Database Insertion

## Overview

The notification service now supports automatic insertion of notification records into the database when FCM notifications are sent. This feature parses specific payload structures and creates corresponding database entries.

## How It Works

When an FCM notification is sent, the system:

1. **Checks the payload structure** - Looks for required fields (`event_type` and `data`)
2. **Parses the payload** - Extracts actor name, room name, and channel information
3. **Creates notification records** - Inserts records for all target users
4. **Continues with FCM delivery** - Sends the push notification regardless of database insertion success

## Required Payload Structure

For automatic database insertion, your FCM payload must include:

```json
{
  "sso_ids": ["user-uuid-1", "user-uuid-2"],
  "data": {
    "event_type": "add_agent",
    "data": "{\"actor_name\":\"<PERSON>\",\"extra\":{\"room\":{\"name\":\"Support Room\",\"channel\":\"wa\"}}}"
  },
  "notification": {
    "title": "Agent Added",
    "body": "<PERSON> has been assigned to Support Room"
  },
  "click_action": "https://app.example.com/room/123"
}
```

### Required Fields

- **`data.event_type`**: Event type (e.g., "add_agent", "message_received")
- **`data.data`**: JSON string containing:
  - `actor_name`: Name of the person performing the action
  - `extra.room.name`: Room/conversation name
  - `extra.room.channel`: Communication channel (e.g., "wa", "telegram")

## Generated Notification

The system automatically generates:

- **Title**: From `notification.title` or auto-generated based on event type
- **Description**: `"<actor_name> assigned you <room_name> on <channel_name>"`
- **Event Type**: From `data.event_type`
- **Click Action**: From the `click_action` field
- **SSO ID**: Each SSO ID from `sso_ids`
- **Notification Type**: Automatically set to "3" (Inbox) for Chat notifications
- **Origin**: Set to "chat" for FCM-generated notifications

## Example Scenarios

### Scenario 1: Agent Assignment
```json
{
  "data": {
    "event_type": "add_agent",
    "data": "{\"actor_name\":\"Kendall Jast DVM\",\"extra\":{\"room\":{\"name\":\"Customer Support\",\"channel\":\"wa\"}}}"
  }
}
```
**Generated Description**: "Kendall Jast DVM assigned you Customer Support on wa"

### Scenario 2: Message Received
```json
{
  "data": {
    "event_type": "message_received",
    "data": "{\"actor_name\":\"Alice Smith\",\"extra\":{\"room\":{\"name\":\"Sales Inquiry\",\"channel\":\"telegram\"}}}"
  }
}
```
**Generated Description**: "Alice Smith assigned you Sales Inquiry on telegram"

## Backward Compatibility

Existing FCM notifications without the required payload structure will:
- ✅ Still be sent via FCM
- ❌ Not create database records
- ✅ Log a debug message about missing fields

Example of non-auto-insertion payload:
```json
{
  "sso_ids": ["user-uuid"],
  "data": {
    "type": "message",
    "message_id": "12345"
  },
  "notification": {
    "title": "New Message",
    "body": "You have a message"
  }
}
```

## Error Handling

The system handles errors gracefully:

1. **Payload parsing fails**: Logs warning, continues with FCM
2. **Database insertion fails**: Logs error, continues with FCM
3. **Missing required fields**: Logs debug message, continues with FCM

## Testing

Use the provided test script:

```bash
./docs/examples/test_notification_insertion.sh
```

Or test manually:

```bash
# Send notification with auto-insertion
curl -X POST http://localhost:4000/api/v1/push-notifications \
  -H "Content-Type: application/json" \
  -d '{
    "sso_ids": ["123e4567-e89b-12d3-a456-************"],
    "data": {
      "event_type": "add_agent",
      "data": "{\"actor_name\":\"John Doe\",\"extra\":{\"room\":{\"name\":\"Support\",\"channel\":\"wa\"}}}"
    },
    "notification": {
      "title": "Agent Added",
      "body": "John Doe assigned to Support"
    },
    "click_action": "https://app.example.com/room/123"
  }'

# Check if notification was created
curl http://localhost:4000/api/v1/notifications
```

## Database Schema

The notification_banks table includes:

- `id`: UUID primary key
- `user_id`: UUID of the recipient
- `description`: Auto-generated description
- `event_type`: From payload (NEW FIELD)
- `click_action`: Always "OPEN_URL"
- `click_action_url`: From payload
- `created_at`, `updated_at`: Timestamps

## Migration

Run the migration to add the `event_type` field:

```bash
migrate -path migrations -database "your-connection-string" up
```

## Logging

The system provides detailed logging:

```
DEBUG Payload does not contain required fields for automatic notification creation
INFO Successfully created notification records count=2 event_type=add_agent
ERROR Failed to create notification batch error="database error"
```

## Performance

- **Batch insertion**: Multiple notifications are inserted in a single transaction
- **Non-blocking**: FCM delivery continues even if database insertion fails
- **Efficient parsing**: Only parses payloads with required structure
