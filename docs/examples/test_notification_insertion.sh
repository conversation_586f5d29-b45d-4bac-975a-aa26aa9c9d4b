#!/bin/bash

# Test script for FCM notification with automatic database insertion
# This script demonstrates how to send notifications that will be automatically
# inserted into the notifications table

BASE_URL="http://localhost:4000"

echo "=== Testing FCM Notification with Automatic Database Insertion ==="
echo

# Step 1: Register an FCM token
echo "1. Registering FCM token..."
curl -X POST "${BASE_URL}/api/v1/fcm-tokens" \
  -H "Content-Type: application/json" \
  -d '{
    "sso_id": "123e4567-e89b-12d3-a456-426614174000",
    "token": "test-token-for-notification-insertion",
    "user_type": "agent",
    "user_source": "chat"
  }' \
  -w "\nStatus: %{http_code}\n\n"

# Step 2: Send notification with correct payload structure for auto-insertion
echo "2. Sending FCM notification with auto-insertion payload..."
curl -X POST "${BASE_URL}/api/v1/push-notifications" \
  -H "Content-Type: application/json" \
  -d '{
    "sso_ids": ["123e4567-e89b-12d3-a456-426614174000"],
    "data": {
      "event_type": "add_agent",
      "data": "{\"actor_name\":\"<PERSON> Jast DVM\",\"extra\":{\"room\":{\"name\":\"Rena Kertzmann\",\"channel\":\"wa\"}}}"
    },
    "notification": {
      "title": "Agent Added",
      "body": "Hal Funk JD has been assigned to room Rena Kertzmann"
    },
    "click_action": "http://localhost:3002/inbox?room=752b3839-8a7c-4a11-bd06-90af1d9b799c"
  }' \
  -w "\nStatus: %{http_code}\n\n"

# Step 3: Send a regular notification (without auto-insertion)
echo "3. Sending regular FCM notification (no auto-insertion)..."
curl -X POST "${BASE_URL}/api/v1/push-notifications" \
  -H "Content-Type: application/json" \
  -d '{
    "sso_ids": ["123e4567-e89b-12d3-a456-426614174000"],
    "data": {
      "type": "message",
      "message_id": "12345"
    },
    "notification": {
      "title": "New Message",
      "body": "You have a new message"
    },
    "click_action": "https://app.example.com/messages/12345"
  }' \
  -w "\nStatus: %{http_code}\n\n"

# Step 4: Check notifications in database
echo "4. Checking notifications in database..."
curl -X GET "${BASE_URL}/api/v1/notifications?limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n\n"

echo "=== Test Complete ==="
echo
echo "Expected results:"
echo "- Step 1: Should return 201 (token registered)"
echo "- Step 2: Should return 200 (notification sent) AND create a record in notifications table"
echo "- Step 3: Should return 200 (notification sent) but NOT create a record in notifications table"
echo "- Step 4: Should show the notification created in step 2 with description: 'Kendall Jast DVM assigned you Rena Kertzmann on wa'"
