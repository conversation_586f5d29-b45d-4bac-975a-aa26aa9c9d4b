# FCM Notification Examples

This document provides practical examples of how to send FCM notifications using your notification service.

## Prerequisites

1. **Firebase Project Setup**: You need Firebase projects set up with FCM enabled
2. **Service Account Keys**: Download service account JSON files from Firebase Console
3. **Environment Configuration**: Set up your `.env` file with FCM configurations

## Environment Configuration

Add the following to your `.env` file:

```env
# Firebase Cloud Messaging Configuration

# CRM FCM Configuration
FCM_CRM_PROJECT_ID=your-crm-firebase-project-id
FCM_CRM_CREDENTIALS_FILE=/path/to/crm-service-account.json

# Chat FCM Configuration  
FCM_CHAT_PROJECT_ID=your-chat-firebase-project-id
FCM_CHAT_CREDENTIALS_FILE=/path/to/chat-service-account.json

# Default FCM Configuration
FCM_DEFAULT_PROJECT_ID=your-default-firebase-project-id
FCM_DEFAULT_CREDENTIALS_FILE=/path/to/default-service-account.json
```

## Step 1: Register FCM Tokens

Before sending notifications, users need to register their FCM tokens:

```bash
curl -X POST http://localhost:4000/api/v1/fcm-tokens \
  -H "Content-Type: application/json" \
  -d '{
    "sso_id": "123e4567-e89b-12d3-a456-************",
    "token": "eyjRPPAE6s_r8VrggDo6Zm:APA91bEUndZewO440SBZMCJzEjQR1PzOyd3v-M13RXYuFj7ofvmrsUwx3g4k2WAY7mWFhvvhuVHCuuK2PlR-OtYeGtGdxYTCyG1t8uet1my6Gr52HNNfUfg",
    "user_type": "admin",
    "user_source": "chat"
  }'
```

### User Sources

- `crm`: Uses FCM_CRM_* configuration
- `chat`: Uses FCM_CHAT_* configuration  
- `notification_service`: Uses FCM_DEFAULT_* configuration

## Step 2: Send FCM Notifications

### Method 1: REST API

Send notifications directly via the REST API:

```bash
curl -X POST http://localhost:4000/api/v1/push-notifications \
  -H "Content-Type: application/json" \
  -d '{
    "sso_ids": ["123e4567-e89b-12d3-a456-************"],
    "data": {
      "type": "message",
      "message_id": "12345",
      "sender": "John Doe"
    },
    "notification": {
      "title": "New Message",
      "body": "You have a new message from John Doe"
    },
    "click_action": "https://app.example.com/messages/12345"
  }'
```

### Method 2: Kafka Producer (Recommended)

For production systems, use Kafka to send notifications:

```go
package main

import (
    "encoding/json"
    "log"
    
    "github.com/IBM/sarama"
    "github.com/google/uuid"
)

type FCMNotificationMessage struct {
    SSOIDs       []uuid.UUID            `json:"sso_ids"`
    Data         map[string]interface{} `json:"data"`
    Notification map[string]string      `json:"notification"`
    ClickAction  string                 `json:"click_action"`
}

func sendNotificationViaKafka() {
    // Create Kafka producer
    config := sarama.NewConfig()
    config.Producer.Return.Successes = true
    
    producer, err := sarama.NewSyncProducer([]string{"localhost:9092"}, config)
    if err != nil {
        log.Fatal(err)
    }
    defer producer.Close()
    
    // Create notification message
    message := FCMNotificationMessage{
        SSOIDs: []uuid.UUID{
            uuid.MustParse("123e4567-e89b-12d3-a456-************"),
        },
        Data: map[string]interface{}{
            "type":       "alert",
            "alert_id":   "alert-123",
            "priority":   "high",
        },
        Notification: map[string]string{
            "title": "System Alert", 
            "body":  "Critical system alert requires your attention",
        },
        ClickAction: "https://admin.example.com/alerts/alert-123",
    }
    
    // Marshal to JSON
    messageBytes, err := json.Marshal(message)
    if err != nil {
        log.Fatal(err)
    }
    
    // Send to Kafka topic
    kafkaMessage := &sarama.ProducerMessage{
        Topic: "qontak_chat.public.fcm_worker",
        Value: sarama.StringEncoder(messageBytes),
    }
    
    partition, offset, err := producer.SendMessage(kafkaMessage)
    if err != nil {
        log.Fatal(err)
    }
    
    log.Printf("Message sent to partition %d with offset %d", partition, offset)
}
```

## Step 3: Start the Services

### Start the API Server

```bash
go run main.go server -s :4000
```

### Start the FCM Worker

```bash
go run main.go worker -t qontak_chat.public.fcm_worker -g fcm-notification-consumers
```

## Advanced Examples

### Sending Notifications with Rich Data

```json
{
  "sso_ids": ["123e4567-e89b-12d3-a456-************"],
  "data": {
    "type": "order_update",
    "order_id": "ORD-12345",
    "status": "shipped",
    "tracking_number": "1Z999AA1234567890",
    "estimated_delivery": "2024-01-15",
    "deep_link": "app://orders/ORD-12345"
  },
  "notification": {
    "title": "Order Shipped! 📦",
    "body": "Your order #ORD-12345 has been shipped and is on its way!"
  },
  "click_action": "https://app.example.com/orders/ORD-12345"
}
```

### Automatic Notification Database Insertion

For notifications that should be automatically stored in the database, use the following payload structure:

```json
{
  "sso_ids": ["123e4567-e89b-12d3-a456-************"],
  "data": {
    "event_type": "add_agent",
    "data": "{\"actor_name\":\"Kendall Jast DVM\",\"extra\":{\"room\":{\"name\":\"Rena Kertzmann\",\"channel\":\"wa\"}}}"
  },
  "notification": {
    "title": "Agent Added",
    "body": "Hal Funk JD has been assigned to room Rena Kertzmann"
  },
  "click_action": "http://localhost:3002/inbox?room=752b3839-8a7c-4a11-bd06-90af1d9b799c"
}
```

This payload structure will:
1. Send the FCM notification to the specified users
2. Automatically create notification records in the database
3. Parse the nested data to extract actor name, room name, and channel
4. Build a description: "Kendall Jast DVM assigned you Rena Kertzmann on wa"

#### Required Fields for Auto-Insertion

- `data.event_type`: The type of event (e.g., "add_agent", "message_received")
- `data.data`: JSON string containing:
  - `actor_name`: Name of the person performing the action
  - `extra.room.name`: Name of the room/conversation
  - `extra.room.channel`: Communication channel (e.g., "wa", "telegram")

#### Example with Different Event Types

```json
{
  "sso_ids": ["123e4567-e89b-12d3-a456-************"],
  "data": {
    "event_type": "message_received",
    "data": "{\"actor_name\":\"John Doe\",\"extra\":{\"room\":{\"name\":\"Customer Support\",\"channel\":\"telegram\"}}}"
  },
  "notification": {
    "title": "New Message",
    "body": "You have a new message in Customer Support"
  },
  "click_action": "http://localhost:3002/inbox?room=abc123"
}
```

### Platform-Specific Customization

The notification service automatically handles platform-specific features:

- **Android**: Uses `click_action` for notification tap handling
- **iOS**: Uses `category` field for custom actions
- **Web**: Uses `link` for browser redirects

### Sending to Multiple User Sources

If users have tokens registered under different sources (CRM, Chat), the service automatically:

1. Groups tokens by user source
2. Uses the appropriate Firebase credentials for each source
3. Sends notifications in parallel for better performance

## Error Handling

The service provides detailed logging for troubleshooting:

```
INFO FCM message sent user_source=crm token_count=5 success_count=4 failure_count=1
WARN FCM message failed for token user_source=crm token_index=2 error=registration token is not a valid FCM registration token
```

Common error scenarios:

- **Invalid tokens**: Tokens that are no longer valid will be logged
- **Configuration errors**: Missing credentials or project IDs
- **Network issues**: Firebase service connectivity problems

## Testing

### Test with curl

```bash
# Register a test token
curl -X POST http://localhost:4000/api/v1/fcm-tokens \
  -H "Content-Type: application/json" \
  -d '{
    "sso_id": "123e4567-e89b-12d3-a456-************",
    "token": "test-token-123",
    "user_type": "admin", 
    "user_source": "crm"
  }'

# Send a test notification
curl -X POST http://localhost:4000/api/v1/push-notifications \
  -H "Content-Type: application/json" \
  -d '{
    "sso_ids": ["123e4567-e89b-12d3-a456-************"],
    "notification": {
      "title": "Test Notification",
      "body": "This is a test message"
    }
  }'
```

### Test with Kafka

Use a Kafka CLI tool or write a simple producer to test the Kafka consumer:

```bash
# Using kafka-console-producer (if available)
echo '{"sso_ids":["123e4567-e89b-12d3-a456-************"],"notification":{"title":"Test","body":"Test message"}}' | \
  kafka-console-producer --broker-list localhost:9092 --topic qontak_chat.public.fcm_worker
```

## Best Practices

1. **Use Kafka for production**: More reliable and scalable than direct API calls
2. **Handle token cleanup**: Remove invalid tokens from your database
3. **Monitor success rates**: Track notification delivery success rates
4. **Batch operations**: Send to multiple users in single requests when possible
5. **Rate limiting**: Be mindful of FCM quotas and rate limits
6. **Error handling**: Implement proper retry logic for failed notifications

## Security Considerations

1. **Secure credentials**: Store Firebase service account keys securely
2. **Validate input**: Always validate SSO IDs and notification content
3. **Access control**: Implement proper authorization for the notification endpoints
4. **Token management**: Regularly clean up expired or invalid tokens 