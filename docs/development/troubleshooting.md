# Troubleshooting Guide

This document provides solutions to common issues encountered when developing or running the Notification Service.

## Table of Contents

1. [Build Issues](#build-issues)
2. [Runtime Issues](#runtime-issues)
3. [Database Issues](#database-issues)
4. [Kafka Issues](#kafka-issues)
5. [API Issues](#api-issues)
6. [Testing Issues](#testing-issues)

## Build Issues

### Go Command Not Found

**Issue**: When running `go` commands, you get `command not found: go`.

**Solution**:
1. Ensure Go is installed: `which go`
2. If Go is installed in a non-standard location, use the full path:
   ```bash
   /path/to/go run main.go server
   ```
3. Update your Makefile to use the correct Go path:
   ```makefile
   build:
       @echo "Building ${APP_NAME} ${VERSION}"
       /path/to/go build -ldflags "..." -o bin/${APP_NAME} -trimpath .
   ```

### Duplicate Handler Definitions

**Issue**: Compilation errors about redeclared types or functions in handler files.

**Solution**:
1. Check for duplicate files defining the same interfaces or structs
2. Remove or rename one of the duplicate files
3. Ensure each handler interface and implementation is defined only once

Example error:
```
internal/app/handler/health.go:8:6: IHealthHandler redeclared in this block
    internal/app/handler/handler.go:9:6: other declaration of IHealthHandler
```

### Missing Configuration Functions

**Issue**: Compilation errors about undefined functions in the config package.

**Solution**:
1. Add the missing functions to the config package
2. Ensure function signatures match their usage
3. Update function calls to match the correct signatures

Example:
```go
// Add to config/config.go
func GetAppName() string {
    return viper.GetString("app.name")
}

func GetAppEnv() string {
    return viper.GetString("app.env")
}
```

### Go Version Issues

**Issue**: Errors related to Go version compatibility.

**Solution**:
1. Check your Go version: `go version`
2. Update the `go` directive in `go.mod` to match your installed version
3. If using an unreleased Go version in `go.mod` (e.g., 1.24.4), update it to the latest stable version

## Runtime Issues

### Server Fails to Start

**Issue**: The server fails to start with various errors.

**Solution**:
1. Check logs for specific error messages
2. Verify configuration in `config.yaml`
3. Ensure required services (database, Kafka) are running
4. Check for port conflicts: `lsof -i :4000`

### Worker Fails to Start

**Issue**: The Kafka worker fails to start.

**Solution**:
1. Check Kafka connection settings
2. Verify topic and consumer group parameters
3. Ensure Kafka is running and accessible
4. Check for permission issues with Kafka topics

### Resource Cleanup Issues

**Issue**: Resources like database connections or Kafka producers aren't properly closed.

**Solution**:
1. Ensure proper defer statements for cleanup
2. Implement graceful shutdown handlers
3. Use context cancellation for coordinated shutdown

Example:
```go
defer func() {
    if kafkaProducer != nil {
        if err := kafkaProducer.Close(); err != nil {
            slog.Error("error while closing kafka producer", slog.Any("error", err))
        }
    }
}()
```

## Database Issues

### Connection Failures

**Issue**: Unable to connect to the PostgreSQL database.

**Solution**:
1. Verify database is running: `docker ps | grep postgres`
2. Check connection parameters in `.env` file
3. Test connection directly: `psql -h localhost -U postgres -d hub_test`
4. Check for network issues: `telnet localhost 5432`

### Migration Errors

**Issue**: Database migrations fail to apply.

**Solution**:
1. Check migration files for syntax errors
2. Verify database user has sufficient privileges
3. Check if migrations were already applied
4. Run migrations manually:
   ```bash
   migrate -path migrations -database "postgresql://postgres:password@127.0.0.1:5432/hub_test?sslmode=disable" up
   ```

### Query Errors

**Issue**: Database queries fail at runtime.

**Solution**:
1. Check for SQL syntax errors
2. Verify table and column names
3. Ensure proper error handling in repository layer
4. Add logging for SQL queries in development mode

## Kafka Issues

### Producer Connection Issues

**Issue**: Unable to connect to Kafka for producing messages.

**Solution**:
1. Verify Kafka is running: `docker ps | grep kafka`
2. Check broker addresses in `.env` file
3. Test connection: `kafkacat -b localhost:9092 -L`
4. Check for network issues: `telnet localhost 9092`

### Consumer Group Issues

**Issue**: Consumer group fails to start or process messages.

**Solution**:
1. Verify consumer group ID is unique
2. Check topic exists: `kafka-topics --list --bootstrap-server localhost:9092`
3. Ensure topic has messages: `kafka-console-consumer --bootstrap-server localhost:9092 --topic example-topic --from-beginning`
4. Check consumer group status: `kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group example-group`

### Message Processing Errors

**Issue**: Errors when processing Kafka messages.

**Solution**:
1. Implement proper error handling in consumer code
2. Add retry logic for transient failures
3. Use dead-letter queues for messages that can't be processed
4. Add detailed logging for message processing

## API Issues

### Endpoint Not Found

**Issue**: API endpoints return 404 Not Found.

**Solution**:
1. Check route registration in `internal/server/route.go`
2. Verify URL path in client requests
3. Ensure handler is properly initialized and added to the app context
4. Check for typos in route paths

### Validation Errors

**Issue**: API requests fail validation.

**Solution**:
1. Check request payload against API documentation
2. Ensure required fields are provided
3. Verify field types and formats
4. Add detailed validation error messages

### Response Format Issues

**Issue**: API responses have unexpected format.

**Solution**:
1. Check response serialization in handlers
2. Verify model struct tags for JSON serialization
3. Ensure consistent response format across endpoints
4. Add middleware for response formatting if needed

## Testing Issues

### Test Failures

**Issue**: Unit or integration tests fail.

**Solution**:
1. Check test logs for specific failure reasons
2. Verify test setup and teardown
3. Ensure mocks behave as expected
4. Check for environment-specific issues

### Mock Issues

**Issue**: Problems with mocking dependencies in tests.

**Solution**:
1. Use interfaces for dependencies to enable mocking
2. Ensure mock implementations match interfaces
3. Verify mock expectations and return values
4. Consider using a mocking library like [testify/mock](https://github.com/stretchr/testify)

### Coverage Issues

**Issue**: Low test coverage.

**Solution**:
1. Identify uncovered code paths: `go tool cover -html=coverage.out`
2. Add tests for uncovered functions
3. Include edge cases and error conditions in tests
4. Set coverage thresholds for CI/CD pipelines
