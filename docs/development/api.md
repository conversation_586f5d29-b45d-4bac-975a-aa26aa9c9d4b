# API Documentation

This document describes the API endpoints provided by the Notification Service.

## Base URL

All API endpoints are relative to the base URL:

```
http://localhost:4000/api/v1
```

## Authentication

The API uses two types of authentication:

1. **SSO Authentication**: For user-facing endpoints, uses `X-Authenticated-Userid` header containing the SSO ID
2. **Internal API Authentication**: For service-to-service communication, uses `X-Api-Key` header with internal API key

The SSO ID is used to identify the current user for user-specific operations and ensures users can only access their own notifications.

## Response Format

All API responses follow a standard format:

### Success Response

```json
{
  "data": <response_data>,
  "meta": <metadata>
}
```

Where:
- `data`: The response data (object, array, or null)
- `meta`: Metadata about the response (pagination info, messages, etc.)

### Error Response

```json
{
  "meta": {},
  "errors": [
    {
      "error": "Error message"
    }
  ]
}
```

Where:
- `errors`: An array of error objects

## Health Check

### Get Health Status

```
GET /health
```

Returns the health status of the service.

#### Response

```json
{
  "data": null,
  "meta": {
    "message": "OK"
  }
}
```

#### Status Codes

- `200 OK`: Service is healthy

## Notifications (notification_banks table)

### Create Chat Notification (Internal API)

```http
POST /notifications/chat
```

Creates a new notification for Chat application. Requires internal API key authentication.

#### Headers

```http
X-Api-Key: <internal-api-key>
```

#### Request Body

```json
{
  "sso_id": "493fc4e6-14b9-4633-95d0-86b2e27c2387",
  "title": "New Message",
  "description": "You have a new message",
  "click_action": "OPEN_URL",
  "click_action_url": "https://example.com/message/123",
  "is_reminder": false,
  "notif_type": "3",
  "notif_category": "category_123",
  "event_type": "add_agent",
  "extra": {},
  "organization_id": "123e4567-e89b-12d3-a456-************",
  "event_id": "123e4567-e89b-12d3-a456-************"
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `sso_id` | UUID | Yes | SSO ID of the user receiving the notification |
| `title` | string | Yes | Notification title |
| `description` | string | Yes | Notification message |
| `click_action` | string | No | Action when notification is clicked (OPEN_URL, OPEN_APP) |
| `click_action_url` | string | No | URL to open (required if click_action is OPEN_URL) |
| `is_reminder` | boolean | No | Whether this is a reminder notification |
| `notif_type` | string | No | Notification type ("3" for Chat) |
| `notif_category` | string | No | Notification category |
| `event_type` | string | No | Type of event that triggered the notification |
| `extra` | object | No | Additional metadata |
| `organization_id` | UUID | No | Organization ID |
| `event_id` | UUID | No | Event ID for tracking |

### Create CRM Notification (Internal API)

```http
POST /notifications/crm
```

Creates a new notification for CRM application. Same as Chat notification but with `notif_type` "1" (General) or "2" (Approval).

#### Response

```json
{
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "description": "New message received",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/messages",
    "read_at": null,
    "is_reminder": false,
    "notif_type_id": "123e4567-e89b-12d3-a456-************",
    "notif_category_id": "123e4567-e89b-12d3-a456-************",
    "created_at": "2025-04-28T14:30:00Z",
    "updated_at": "2025-04-28T14:30:00Z"
  },
  "meta": {}
}
```

#### Status Codes

- `201 Created`: Notification created successfully
- `400 Bad Request`: Invalid request body
- `500 Internal Server Error`: Server error

### Get All Notifications

```http
GET /notifications
```

Retrieves all notifications with pagination and filtering for the authenticated user.

#### Headers

```http
X-Authenticated-Userid: <sso-id>
```

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | integer | No | 1 | Page number |
| `limit` | integer | No | 10 | Number of items per page (max 100) |
| `origin` | string | No | - | Filter by origin ('chat' or 'crm') |
| `status` | string | No | - | Filter by read status ('read' or 'unread') |
| `notif_type` | string | No | - | Filter by notification type ('1', '2', or '3') |
| `notif_category` | string | No | - | Filter by notification category |
| `time_offset` | integer | No | 0 | Time offset in hours for timezone adjustment (-14 to 14) |
| `language` | string | No | en | Language for title/description ('en' or 'id') |

#### Response

```json
{
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "user_id": "123e4567-e89b-12d3-a456-************",
      "description": "New message received",
      "click_action": "OPEN_URL",
      "click_action_url": "https://example.com/messages",
      "read_at": null,
      "is_reminder": false,
      "notif_type_id": "123e4567-e89b-12d3-a456-************",
      "notif_category_id": "123e4567-e89b-12d3-a456-************",
      "created_at": "2025-04-28T14:30:00Z",
      "updated_at": "2025-04-28T14:30:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_page": 1
  }
}
```

#### Status Codes

- `200 OK`: Notifications retrieved successfully
- `500 Internal Server Error`: Server error

### Get Notification by ID

```http
GET /notifications/{id}
```

Retrieves a notification by ID. Only returns the notification if it belongs to the authenticated user.

#### Headers

```http
X-Authenticated-Userid: <sso-id>
```

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | UUID | Yes | Notification ID |

#### Response

```json
{
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "description": "New message received",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/messages",
    "read_at": null,
    "is_reminder": false,
    "notif_type_id": "123e4567-e89b-12d3-a456-************",
    "notif_category_id": "123e4567-e89b-12d3-a456-************",
    "created_at": "2025-04-28T14:30:00Z",
    "updated_at": "2025-04-28T14:30:00Z"
  },
  "meta": {}
}
```

#### Status Codes

- `200 OK`: Notification retrieved successfully
- `400 Bad Request`: Invalid notification ID
- `404 Not Found`: Notification not found
- `500 Internal Server Error`: Server error

### Mark Notification as Read

```http
PUT /notifications/mark_as_read/{id}
```

Marks a specific notification as read for the authenticated user.

#### Headers

```http
X-Authenticated-Userid: <sso-id>
```

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | UUID | Yes | Notification ID |

#### Response

```json
{
  "data": null,
  "meta": {
    "message": "Notification marked as read successfully"
  }
}
```

#### Status Codes

- `200 OK`: Notification marked as read successfully
- `400 Bad Request`: Invalid notification ID
- `500 Internal Server Error`: Server error

### Mark All Notifications as Read

```http
PUT /notifications/mark_all_as_read
```

Marks all notifications as read for the authenticated user.

#### Headers

```http
X-Authenticated-Userid: <sso-id>
```

#### Response

```json
{
  "data": null,
  "meta": {
    "message": "All notifications marked as read successfully"
  }
}
```

#### Status Codes

- `200 OK`: All notifications marked as read successfully
- `400 Bad Request`: Invalid SSO ID
- `500 Internal Server Error`: Server error

### Count Unread Notifications

```http
GET /notifications/unread/count
```

Gets the count of unread notifications for the authenticated user.

#### Headers

```http
X-Authenticated-Userid: <sso-id>
```

#### Response

```json
{
  "data": {
    "count": 5
  },
  "meta": {}
}
```

#### Status Codes

- `200 OK`: Count retrieved successfully
- `400 Bad Request`: Invalid SSO ID
- `500 Internal Server Error`: Server error

## Error Codes

The API uses standard HTTP status codes to indicate the success or failure of a request:

| Status Code | Description |
|-------------|-------------|
| `200 OK` | The request was successful |
| `201 Created` | The resource was successfully created |
| `400 Bad Request` | The request was invalid or cannot be served |
| `404 Not Found` | The requested resource does not exist |
| `500 Internal Server Error` | An error occurred on the server |

## API Implementation

The API is implemented using the following components:

### Handlers

Handlers are responsible for processing HTTP requests and returning responses:

```go
// NotificationHandler defines the interface for notification handler operations
type NotificationHandler interface {
    CreateNotification(w http.ResponseWriter, r *http.Request)
    GetNotificationByID(w http.ResponseWriter, r *http.Request)
    GetNotifications(w http.ResponseWriter, r *http.Request)
    MarkAsRead(w http.ResponseWriter, r *http.Request)
    MarkAllAsRead(w http.ResponseWriter, r *http.Request)
    CountUnread(w http.ResponseWriter, r *http.Request)
}
```

### Routes

Routes are defined in `internal/server/route.go`:

```go
func (r *Router) registerV1Route(chiRouter chi.Router) {
    chiRouter.Route("/v1", func(rt chi.Router) {
        // Health routes
        rt.Route("/health", func(rt2 chi.Router) {
            rt2.Get("/", r.AppContext.HealthHandler.HealthCheck)
        })

        // Notification routes
        if r.AppContext.NotificationHandler != nil {
            rt.Route("/notifications", func(rt2 chi.Router) {
                rt2.Post("/", r.AppContext.NotificationHandler.CreateNotification)
                rt2.Get("/", r.AppContext.NotificationHandler.GetNotifications)
                rt2.Get("/{id}", r.AppContext.NotificationHandler.GetNotificationByID)
                rt2.Put("/mark_all_as_read", r.AppContext.NotificationHandler.MarkAllAsRead)
                rt2.Put("/mark_as_read/{id}", r.AppContext.NotificationHandler.MarkAsRead)
                rt2.Get("/unread/count", r.AppContext.NotificationHandler.CountUnread)
            })
        }
    })
}
```

## Testing the API

You can test the API using curl or any API client:

### Health Check

```bash
curl http://localhost:4000/api/v1/health
```

### Create Chat Notification (Internal API)

```bash
curl -X POST http://localhost:4000/api/v1/notifications/chat \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: your-internal-api-key" \
  -d '{
    "sso_id": "493fc4e6-14b9-4633-95d0-86b2e27c2387",
    "title": "New Message",
    "description": "You have a new message",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/message/123",
    "is_reminder": false,
    "notif_type": "3",
    "notif_category": "category_123",
    "event_type": "add_agent"
  }'
```

### Get All Notifications with Filtering

```bash
# Basic request
curl -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications?page=1&limit=10

# With filtering and Indonesian language
curl -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  "http://localhost:4000/api/v1/notifications?page=1&limit=10&origin=chat&status=unread&language=id&time_offset=7"
```

### Get Notification by ID

```bash
curl -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications/123e4567-e89b-12d3-a456-************
```

### Mark Notification as Read

```bash
curl -X PUT -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications/mark_as_read/123e4567-e89b-12d3-a456-************
```

### Mark All Notifications as Read

```bash
curl -X PUT -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications/mark_all_as_read
```

### Count Unread Notifications

```bash
curl -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications/unread/count
```

## Future API Enhancements

The following enhancements are planned for future releases:

1. **Notification Types**: Add endpoints to manage notification types
2. **Notification Categories**: Add endpoints to manage notification categories
3. **Websocket Support**: Real-time notification delivery
4. **Bulk Operations**: Batch create and update operations
5. **Advanced Filtering**: Add filtering by date range, notification type, etc.
6. **Push Notifications**: Integration with FCM for real-time push notifications

## Recently Implemented Features

The following features have been recently implemented:

1. **✅ Dual Authentication**: SSO-based authentication for users and API key authentication for internal services
2. **✅ User-Specific Notifications**: Notifications are filtered by SSO ID with security validation
3. **✅ Mark as Read**: Endpoints to mark individual and all notifications as read
4. **✅ Unread Count**: Endpoint to get count of unread notifications for current user
5. **✅ Advanced Filtering**: Filter notifications by origin, read status, type, and category
6. **✅ Internationalization**: Support for English and Indonesian languages with `title_alt` and `description_alt`
7. **✅ Static Notification Types**: Simplified notification types (1=General, 2=Approval, 3=Inbox) with retention policies
8. **✅ Internal API Endpoints**: Separate endpoints for Chat and CRM applications with service-to-service authentication
9. **✅ Timezone Support**: Time offset parameter for proper timezone handling
10. **✅ Enhanced Security**: User can only access their own notifications with SSO validation
