# New Features and Recent Updates

This document outlines the major features and improvements that have been recently implemented in the Notification Service.

## Overview of Recent Changes

The Notification Service has undergone significant enhancements to improve functionality, security, and internationalization support. Below are the key updates:

## 1. Dual Authentication System

### SSO Authentication (User-facing endpoints)
- **Header**: `X-Authenticated-Userid: <sso-id>`
- **Purpose**: Authenticates end users accessing their notifications
- **Security**: Users can only access their own notifications

### Internal API Authentication (Service-to-service)
- **Header**: `X-Api-Key: <internal-api-key>`
- **Purpose**: Authenticates internal services (Chat, CRM) creating notifications
- **Endpoints**: `/api/v1/notifications/chat` and `/api/v1/notifications/crm`

## 2. Static Notification Types

The system now uses predefined notification types instead of a dynamic database table:

```go
const (
    NotificationTypeGeneral  NotificationType = "1"  // CRM General (90 days retention)
    NotificationTypeApproval NotificationType = "2"  // CRM Approval (90 days retention)
    NotificationTypeInbox    NotificationType = "3"  // Chat Inbox (7 days retention)
)
```

### Retention Policies
- **General & Approval (CRM)**: 90 days
- **Inbox (Chat)**: 7 days

## 3. Internationalization Support

### Database Schema
```sql
title_alt TEXT,          -- Alternative title for Indonesian language
description_alt TEXT,    -- Alternative description for Indonesian language
```

### API Usage
- **Parameter**: `language=en|id` (default: `en`)
- **Behavior**: Falls back to main `title`/`description` if `title_alt`/`description_alt` are empty
- **Supported Languages**: English (`en`) and Indonesian (`id`)

## 4. Advanced Filtering

The `GET /api/v1/notifications` endpoint now supports comprehensive filtering:

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `origin` | string | Filter by origin | `chat`, `crm` |
| `status` | string | Filter by read status | `read`, `unread` |
| `notif_type` | string | Filter by notification type | `1`, `2`, `3` |
| `notif_category` | string | Filter by category | `category_123` |
| `time_offset` | integer | Timezone offset in hours | `-7`, `7` |
| `language` | string | Response language | `en`, `id` |

## 5. Enhanced Database Schema

### notification_banks Table
```sql
CREATE TABLE IF NOT EXISTS notification_banks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sso_id UUID NOT NULL,
    title TEXT NOT NULL,                    -- NEW: Required title field
    description TEXT NOT NULL,
    title_alt TEXT,                         -- NEW: Indonesian title
    description_alt TEXT,                   -- NEW: Indonesian description
    click_action VARCHAR(50),
    click_action_url TEXT,
    read_at TIMESTAMP,
    is_reminder BOOLEAN DEFAULT FALSE,
    notif_type_id TEXT NOT NULL,           -- CHANGED: From UUID to TEXT
    notif_category_id UUID REFERENCES notification_categories(id) ON DELETE SET NULL,
    event_type VARCHAR(100),               -- NEW: Event type tracking
    origin VARCHAR(50) CHECK (origin IN ('chat', 'crm')),
    extra JSONB,
    organization_id UUID,
    event_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### Key Changes
- Added `title` field (required)
- Added `title_alt` and `description_alt` for internationalization
- Changed `notif_type_id` from UUID to TEXT
- Added foreign key relationship with `notification_categories`
- Added `event_type` for better event tracking

## 6. Security Enhancements

### User Data Isolation
- All user-facing endpoints validate SSO ID
- Users can only access their own notifications
- `GetNotificationByID` includes SSO validation

### Service Authentication
- Internal endpoints require API key authentication
- Separate endpoints for different services (Chat vs CRM)
- Input validation for notification types per service

## 7. API Endpoint Changes

### New Internal Endpoints
```http
POST /api/v1/notifications/chat    # Chat notifications (notif_type: "3")
POST /api/v1/notifications/crm     # CRM notifications (notif_type: "1" or "2")
```

### Enhanced User Endpoints
```http
GET /api/v1/notifications          # Now supports filtering and language
GET /api/v1/notifications/{id}     # Now includes SSO validation
PUT /api/v1/notifications/mark_as_read/{id}
PUT /api/v1/notifications/mark_all_as_read
GET /api/v1/notifications/unread/count
```

## 8. Migration Strategy

The project uses a single merged migration for clean database setup:

```bash
# Run the migration
./scripts/migrate.sh --host localhost --user postgres --password password --database notification_service
```

This creates all tables with their final schema, including:
- `notification_banks` (main notifications table)
- `notification_categories` (with seeded data)
- `fcm_tokens`

## 9. Testing Examples

### Create Chat Notification
```bash
curl -X POST http://localhost:4000/api/v1/notifications/chat \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: your-internal-api-key" \
  -d '{
    "sso_id": "493fc4e6-14b9-4633-95d0-86b2e27c2387",
    "title": "New Message",
    "description": "You have a new message",
    "notif_type": "3"
  }'
```

### Get Notifications with Indonesian Language
```bash
curl -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  "http://localhost:4000/api/v1/notifications?language=id&origin=chat&status=unread"
```

## 10. Future Considerations

- **WebSocket Support**: Real-time notification delivery
- **Bulk Operations**: Batch create and update operations
- **Advanced Analytics**: Notification engagement tracking
- **Additional Languages**: Expand beyond English and Indonesian
- **Push Notification Integration**: Enhanced FCM integration

## Migration Notes

- Historical migrations have been consolidated into a single file
- Database schema is optimized for the current feature set
- All new installations should use the merged migration
- Existing installations should follow the incremental migration path (if applicable)
