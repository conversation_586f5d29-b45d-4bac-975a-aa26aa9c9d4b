# Development Guide

This document provides comprehensive information for developers working on the Notification Service.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Development Environment Setup](#development-environment-setup)
3. [Architecture](#architecture)
4. [Code Structure](#code-structure)
5. [Configuration](#configuration)
6. [Running the Application](#running-the-application)
7. [Testing](#testing)
8. [Common Issues and Solutions](#common-issues-and-solutions)
9. [Contributing Guidelines](#contributing-guidelines)
10. [Recent Changes](#recent-changes)

## Project Overview

The Notification Service is a microservice responsible for managing user notifications. It provides APIs for creating, retrieving, and managing notifications, and can be integrated with other services via Kafka.

### Key Features

- Create and store user notifications
- Retrieve notifications with pagination
- Mark notifications as read
- Support for different notification types and categories
- Integration with Kafka for event-driven architecture

## Development Environment Setup

### Prerequisites

- Go 1.24.4 or later
- Docker and Docker Compose
- PostgreSQL client tools
- Git

### Initial Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd notification-service
   ```

2. Create an environment file:
   ```bash
   cp .env.example .env
   ```

   If `.env.example` doesn't exist, create a `.env` file with the following content:
   ```
   # Application Configuration
   APP_NAME=notification-service
   APP_ENV=development

   # Database Configuration
   DATABASE_USERNAME=postgres
   DATABASE_PASSWORD=password
   DATABASE_HOST=localhost
   DATABASE_PORT=5432
   DATABASE_NAME=hub_test
   DATABASE_POOL=10
   DATABASE_IDLE_TIMEOUT=1m

   # Kafka Configuration
   KAFKA_BROKERS=localhost:9092
   KAFKA_SASL=false
   KAFKA_USERNAME=
   KAFKA_PASSWORD=
   KAFKA_PREFIX=
   ```

3. Start required services using Docker:
   ```bash
   # Start PostgreSQL
   docker run --name postgres -e POSTGRES_PASSWORD=password -p 5432:5432 -d postgres:14

   # Start Kafka (if needed)
   docker-compose up -d kafka
   ```

4. Run database migrations:
   ```bash
   migrate -path migrations -database "postgresql://postgres:password@127.0.0.1:5432/hub_test?sslmode=disable" up
   ```

## Architecture

The Notification Service follows a clean architecture approach, separating concerns into distinct layers:

### Layers

1. **Presentation Layer (Handlers)**
   - Handles HTTP requests and responses
   - Validates input
   - Calls appropriate service methods
   - Located in `internal/app/handler/`

2. **Business Logic Layer (Services)**
   - Contains business logic
   - Orchestrates data access
   - Performs validations
   - Located in `internal/app/service/`

3. **Data Access Layer (Repositories)**
   - Abstracts database operations
   - Implements CRUD operations
   - Maps database entities to domain models
   - Located in `internal/app/repository/`

4. **Domain Layer (Models)**
   - Contains core business entities
   - Implements entity-specific business rules
   - Located in `internal/app/model/`

### Request Flow

1. HTTP request is received by the handler
2. Handler validates the request and calls the appropriate service method
3. Service executes business logic and calls repository methods
4. Repository interacts with the database
5. Response flows back through the layers to the client

## Code Structure

```
notification-service/
├── cmd/                    # Command-line entry points
│   ├── root.go             # Root command
│   ├── server/             # Server command
│   │   └── server.go       # Server implementation
│   ├── worker/             # Worker command
│   │   └── worker.go       # Worker implementation
│   └── version.go          # Version command
├── config/                 # Configuration management
│   ├── config.go           # Configuration loading
│   ├── database.go         # Database configuration
│   ├── kafka.go            # Kafka configuration
│   └── log.go              # Logging configuration
├── docs/                   # Documentation
├── internal/               # Internal application code
│   ├── app/                # Application core
│   │   ├── handler/        # HTTP handlers
│   │   ├── model/          # Domain models
│   │   ├── repository/     # Data access layer
│   │   └── service/        # Business logic
│   ├── pkg/                # Internal packages
│   │   └── http/           # HTTP utilities
│   └── server/             # HTTP server setup
│       ├── api.go          # API server
│       ├── context.go      # Application context
│       └── route.go        # Route registration
├── migrations/             # Database migrations
├── pkg/                    # Public packages
│   └── kafka/              # Kafka utilities
├── version/                # Version information
├── config.yaml             # Configuration file
├── go.mod                  # Go module definition
├── go.sum                  # Go module checksums
├── main.go                 # Application entry point
├── Makefile                # Build automation
└── README.md               # Project README
```

## Configuration

The application uses [Viper](https://github.com/spf13/viper) and [godotenv](https://github.com/joho/godotenv) for configuration management. Configuration can be provided via:

1. Environment variables (loaded from `.env` file)
2. System environment variables
3. Command-line flags

### Environment File

The application uses a `.env` file in the project root directory to load environment variables. Here's an example:

```
# Application Configuration
APP_NAME=notification-service
APP_ENV=development

# Database Configuration
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=hub_test
DATABASE_POOL=10
DATABASE_IDLE_TIMEOUT=1m
DATABASE_CONN_MAX_LIFETIME=15m
DATABASE_CONN_MAX_IDLE_TIME=5m
DATABASE_MAX_OPEN_CONN=10
DATABASE_MAX_IDLE_CONN=5

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_SASL=false
KAFKA_USERNAME=
KAFKA_PASSWORD=
KAFKA_PREFIX=
KAFKA_CERT=

# Datadog Configuration
DD_AGENT_HOST=localhost
DD_AGENT_PORT=8126
STATSD_HOST=localhost
STATSD_PORT=8125
DATADOG_TRACER=false
```

### Environment Variables

The environment variables follow a specific naming convention:

- Sections are separated by underscores
- All characters are uppercase

For example:
- Application name is set with `APP_NAME`
- Database username is set with `DATABASE_USERNAME`
- Kafka brokers are set with `KAFKA_BROKERS`

## Running the Application

### Running the API Server

Using the Makefile:
```bash
make run ARGS=server
```

Or directly:
```bash
go run main.go server
```

With a custom address:
```bash
go run main.go server -s :8080
```

### Running the Worker

Using the Makefile:
```bash
make run ARGS="worker -t fcm-notifications -g fcm-notification-consumers"
```

Or directly:
```bash
go run main.go worker -t fcm-notifications -g fcm-notification-consumers
```

### Building the Application

```bash
make build
```

This creates a binary in the `bin/` directory.

## Testing

### Running Tests

```bash
make test
```

### Test Coverage

To see test coverage:
```bash
make coverage
```

### Writing Tests

- Place tests in the same package as the code being tested
- Use the `_test.go` suffix for test files
- Use table-driven tests where appropriate
- Mock external dependencies
- Aim for high test coverage, especially for business logic

## Common Issues and Solutions

### Database Connection Issues

**Issue**: Unable to connect to the database.

**Solution**:
1. Ensure PostgreSQL is running: `docker ps | grep postgres`
2. Check database configuration in `config.yaml`
3. Verify network connectivity: `telnet localhost 5432`

### Kafka Connection Issues

**Issue**: Unable to connect to Kafka.

**Solution**:
1. Ensure Kafka is running: `docker ps | grep kafka`
2. Check Kafka configuration in `config.yaml`
3. Verify network connectivity: `telnet localhost 9092`

### Build Errors

**Issue**: Compilation errors when building the application.

**Solution**:
1. Ensure Go version is 1.24.4 or later: `go version`
2. Update dependencies: `go mod tidy`
3. Check for syntax errors: `go vet ./...`

### Handler Duplication Issues

**Issue**: Duplicate handler definitions causing compilation errors.

**Solution**:
1. Check for duplicate interface or struct definitions in the handler package
2. Ensure each handler is defined only once
3. Use proper package organization to avoid duplication

## Contributing Guidelines

### Code Style

- Follow Go's [official style guide](https://golang.org/doc/effective_go)
- Use `gofmt` or `goimports` to format code
- Keep functions small and focused
- Write meaningful comments
- Use descriptive variable and function names

### Pull Request Process

1. Create a feature branch from `main`
2. Make your changes
3. Run tests: `make test`
4. Run linters: `make lint`
5. Run security checks: `make sec`
6. Submit a pull request
7. Address review comments

### Commit Messages

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

Types:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code changes that neither fix bugs nor add features
- `test`: Adding or modifying tests
- `chore`: Changes to the build process or tools

## Recent Changes

### FCM Kafka Consumer Implementation (May 2025)

The FCM (Firebase Cloud Messaging) notification system has been updated to use a Kafka consumer instead of a REST API. This change provides several benefits:

1. **Event-Driven Architecture**: Better integration with event-driven systems
2. **Decoupling**: Decouples the notification sending from the API layer
3. **Scalability**: Allows for better scaling of notification processing
4. **Reliability**: Provides message persistence and retry capabilities

For more details, see the [FCM Kafka Consumer](fcm_kafka_consumer.md) documentation.

### Configuration System Update (April 2025)

The configuration system has been updated to use environment variables loaded from a `.env` file instead of a YAML configuration file. This change provides several benefits:

1. **Standardization**: Using environment variables is a more standard approach for Go applications
2. **Deployment Flexibility**: Makes it easier to deploy in different environments
3. **Security**: Better separation of configuration from code
4. **Compatibility**: Better compatibility with container orchestration systems

#### Migration Notes

If you were previously using `config.yaml`, you'll need to:

1. Create a `.env` file based on the `.env.example` template
2. Convert your configuration values to the new format:
   - Replace nested keys with uppercase, underscore-separated names
   - For example, `app.name` becomes `APP_NAME`
   - For arrays like Kafka brokers, use comma-separated values

#### Implementation Details

The configuration system now uses:
- [godotenv](https://github.com/joho/godotenv) to load variables from `.env` file
- [Viper](https://github.com/spf13/viper) to manage environment variables and provide defaults
- Environment variable naming convention (uppercase with underscores)

The `config.Init()` function now:
1. Loads the `.env` file if it exists
2. Configures Viper to read from environment variables
3. Sets default values for configuration options
