# Database Migrations

This directory contains database migration files for the Notification Service.

## Migration Strategy

This project uses a **single merged migration** approach for clean database setup. All historical migrations have been consolidated into one comprehensive migration.

### For Fresh Database Installation

Run the merged migration to create all tables with their final schema:

```bash
# Run the merged migration
migrate -path migrations -database "your-connection-string" up 1
```

This will run the `20250712000000_create_all_tables_merged.up.sql` migration which creates all tables with their final schema in one go.

## Migration Files

### Current Migration
- `20250712000000_create_all_tables_merged` - **Complete schema from scratch**

This single migration creates all tables with their final, optimized schema. Historical migrations have been removed to simplify database setup.

## Tables Created

### notification_banks
Main table for storing notifications with the following key features:
- Uses `sso_id` instead of `user_id` for authentication
- Optional `click_action` field
- Support for `origin` ('chat' or 'crm')
- JSONB `extra` field for metadata
- Organization and event tracking
- Comprehensive indexing for performance

### fcm_tokens
Stores Firebase Cloud Messaging tokens for push notifications.

### notification_types
Defines notification types with origin-specific values.

### notification_categories  
Defines notification categories with origin-specific values.

## Important Notes

1. **Single Migration**: Only one migration file exists - use it for all new database installations
2. **Clean Start**: This approach ensures a clean, optimized database schema from the beginning
3. **Table Name**: The main table is called `notification_banks` (not `notifications`)
4. **Indexes**: All tables have appropriate indexes for optimal performance
5. **Constraints**: Proper CHECK constraints ensure data integrity
6. **No Historical Baggage**: No legacy schema issues or incremental migration complexity

## Connection String Examples

```bash
# PostgreSQL
migrate -path migrations -database "postgresql://user:password@localhost:5432/dbname?sslmode=disable" up 1

# With SSL
migrate -path migrations -database "postgresql://user:password@localhost:5432/dbname?sslmode=require" up 1

# With custom migration table name
migrate -path migrations -database "postgresql://user:password@localhost:5432/dbname?sslmode=disable&x-migrations-table=my_custom_migrations" up 1

# Using the migration script (easiest)
./scripts/migrate.sh --host localhost --user myuser --password mypass --database mydb

# Using the migration script with custom migration table
./scripts/migrate.sh --host localhost --user myuser --password mypass --database mydb --migrations-table my_custom_migrations
```

## Custom Migration Table Name

By default, golang-migrate uses `schema_migrations` as the table name to track migration history. You can customize this in several ways:

### Method 1: Query Parameter
Add `x-migrations-table=your_table_name` to the connection string:
```bash
migrate -path migrations -database "postgresql://user:pass@host:port/db?x-migrations-table=my_migrations" up 1
```

### Method 2: Environment Variable
```bash
export MIGRATIONS_TABLE=my_migrations
migrate -path migrations -database "postgresql://user:pass@host:port/db" up 1
```

### Method 3: Migration Script
```bash
./scripts/migrate.sh --migrations-table my_migrations
```

## Rollback

To rollback the migration:

```bash
migrate -path migrations -database "your-connection-string" down 1
```

This will drop all tables created by the migration.
