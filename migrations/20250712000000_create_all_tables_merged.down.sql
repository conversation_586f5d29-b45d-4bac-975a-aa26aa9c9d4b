-- =====================================================
-- MERGED MIGRATION ROLLBACK: Drop all tables
-- This migration drops all tables created by the merged migration
-- =====================================================

-- Drop indexes for notification_categories table
DROP INDEX IF EXISTS idx_notification_categories_origin_value;
DROP INDEX IF EXISTS idx_notification_categories_origin;

-- Drop notification_categories table
DROP TABLE IF EXISTS notification_categories;

-- Drop indexes for fcm_tokens table
DROP INDEX IF EXISTS idx_fcm_tokens_token;
DROP INDEX IF EXISTS idx_fcm_tokens_sso_id;

-- Drop fcm_tokens table
DROP TABLE IF EXISTS fcm_tokens;

-- Drop indexes for notification_banks table
DROP INDEX IF EXISTS idx_notification_banks_event_id;
DROP INDEX IF EXISTS idx_notification_banks_organization_id;
DROP INDEX IF EXISTS idx_notification_banks_notif_category_id;
DROP INDEX IF EXISTS idx_notification_banks_notif_type_id;
DROP INDEX IF EXISTS idx_notification_banks_read_at;
DROP INDEX IF EXISTS idx_notification_banks_origin;
DROP INDEX IF EXISTS idx_notification_banks_sso_id;
DROP INDEX IF EXISTS idx_notification_banks_created_at;

-- Drop notification_banks table
DROP TABLE IF EXISTS notification_banks;

-- =====================================================
-- ROLLBACK COMPLETE
-- All tables and indexes dropped
-- =====================================================
