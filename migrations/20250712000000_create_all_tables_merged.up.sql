-- =====================================================
-- MERGED MIGRATION: Create all tables from scratch
-- This migration creates all tables with their final schema
-- Use this for fresh database installations
-- =====================================================
BEGIN;

-- Create fcm_tokens table
CREATE TABLE IF NOT EXISTS fcm_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sso_id UUID NOT NULL,
    token TEXT NOT NULL,
    user_type VARCHAR(50) NOT NULL,
    user_source VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes for fcm_tokens table
CREATE INDEX IF NOT EXISTS idx_fcm_tokens_sso_id ON fcm_tokens(sso_id);
CREATE INDEX IF NOT EXISTS idx_fcm_tokens_token ON fcm_tokens(token);


-- Create notification_categories table
CREATE TABLE IF NOT EXISTS notification_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    origin VARCHAR(50) NOT NULL CHECK (origin IN ('chat', 'crm')),
    origin_value TEXT NOT NULL,  -- Changed to text. Should be able to handle string and integers
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE(origin, origin_value)
);

-- Create indexes for notification_categories table
CREATE INDEX IF NOT EXISTS idx_notification_categories_origin ON notification_categories(origin);
CREATE INDEX IF NOT EXISTS idx_notification_categories_origin_value ON notification_categories(origin, origin_value);

-- Seeding for notification_categories table
INSERT INTO notification_categories (name, origin, origin_value) VALUES
  ('Reminder', 'crm', '1'),
  ('Mention', 'crm', '2'),
  ('Assignment', 'crm', '3'),
  ('Complete', 'crm', '4'),
  ('Download & Upload', 'crm', '5'),
  ('Upload', 'crm', '6'),
  ('Other', 'crm', '7'),
  ('Deals', 'crm', '8'),
  ('Expenses', 'crm', '9'),
  ('Incoming Chat', 'chat', '10'),
  ('Chat Assignment', 'chat', '11');

-- Create notification_banks table (formerly notifications)
-- This includes all schema changes from previous migrations
CREATE TABLE IF NOT EXISTS notification_banks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sso_id UUID NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    title_alt TEXT,  -- Alternative title for Indonesian language
    description_alt TEXT,  -- Alternative description for Indonesian language
    click_action VARCHAR(50),  -- Made optional
    click_action_url TEXT,
    read_at TIMESTAMP,
    is_reminder BOOLEAN DEFAULT FALSE,
    notif_type_id TEXT NOT NULL,
    notif_category_id UUID REFERENCES notification_categories(id) ON DELETE SET NULL,
    event_type VARCHAR(100),  -- Added in later migration
    origin VARCHAR(50) CHECK (origin IN ('chat', 'crm')),  -- Added constraint
    extra JSONB,  -- Added for additional metadata
    organization_id UUID,  -- Added for organization support
    event_id UUID,  -- Added for event tracking
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes for notification_banks table
CREATE INDEX IF NOT EXISTS idx_notification_banks_sso_id ON notification_banks(sso_id);
CREATE INDEX IF NOT EXISTS idx_notification_banks_origin ON notification_banks(origin);
CREATE INDEX IF NOT EXISTS idx_notification_banks_read_at ON notification_banks(read_at);
CREATE INDEX IF NOT EXISTS idx_notification_banks_notif_type_id ON notification_banks(notif_type_id);
CREATE INDEX IF NOT EXISTS idx_notification_banks_notif_category_id ON notification_banks(notif_category_id);
CREATE INDEX IF NOT EXISTS idx_notification_banks_organization_id ON notification_banks(organization_id);
CREATE INDEX IF NOT EXISTS idx_notification_banks_event_id ON notification_banks(event_id);
CREATE INDEX IF NOT EXISTS idx_notification_banks_created_at ON notification_banks(created_at);

-- Create unique index for idempotency based on sso_id and event_id
CREATE UNIQUE INDEX IF NOT EXISTS sso_event_id_idempotent ON notification_banks(sso_id, event_id) WHERE event_id IS NOT NULL;

COMMIT;
-- =====================================================
-- MIGRATION COMPLETE
-- All tables created with their final schema
-- =====================================================
