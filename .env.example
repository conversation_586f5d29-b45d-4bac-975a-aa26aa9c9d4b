# Application Configuration
APP_NAME=notification-service
APP_ENV=development

# Internal API Key
INTERNAL_API_KEY=your_api_key_here

# Database Configuration
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=hub_test
DATABASE_POOL=10
DATABASE_IDLE_TIMEOUT=1m
DATABASE_CONN_MAX_LIFETIME=15m
DATABASE_CONN_MAX_IDLE_TIME=5m
DATABASE_MAX_OPEN_CONN=10
DATABASE_MAX_IDLE_CONN=5

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_SASL=false
KAFKA_USERNAME=
KAFKA_PASSWORD=
KAFKA_PREFIX=
KAFKA_CERT=

# Datadog Configuration
DD_AGENT_HOST=localhost
DD_AGENT_PORT=8126
STATSD_HOST=localhost
STATSD_PORT=8125
DATADOG_TRACER=false

# FCM Configuration
FCM_CRM_PROJECT_ID=crm-project-id
FCM_CRM_CREDENTIALS_FILE=/path/to/crm-credentials.json
FCM_CHAT_PROJECT_ID=chat-project-id
FCM_CHAT_CREDENTIALS_FILE=/path/to/chat-credentials.json
FCM_DEFAULT_PROJECT_ID=default-project-id
FCM_DEFAULT_CREDENTIALS_FILE=/path/to/default-credentials.json
