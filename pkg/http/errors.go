package domains

import (
	"errors"

	"github.com/go-playground/validator/v10"
)

var (
	ErrInternalServerError = errors.New("internal server error")
	ErrNotFound            = errors.New("your requested item is not found")
	ErrConflict            = errors.New("your item already exist")
	ErrBadRequest          = errors.New("given param is not valid")
	ErrUnprocessableEntity = errors.New("unprocessable entity")
	ErrUnauthenticated     = errors.New("unauthenticated")
	ErrUnauthorized        = errors.New("unauthorized")
)

func FormatValidationErrors(err error) ([]interface{}, error) {
	validationErrors, ok := err.(validator.ValidationErrors)
	if !ok {
		return make([]interface{}, 0), err
	}

	var errs []interface{}

	for _, err := range validationErrors {
		errs = append(errs, map[string]string{"error": err.Error()})
	}

	return errs, nil
}

type InternalErrorException struct {
	Err string
}

func NewInternalErrorException(err string) *InternalErrorException {
	return &InternalErrorException{
		Err: err,
	}
}

func (e *InternalErrorException) Error() string {
	return e.Err
}

type UnauthorizedException struct{}

func NewUnauthorizedException() *UnauthorizedException {
	return &UnauthorizedException{}
}

func (e *UnauthorizedException) Error() string {
	return ErrUnauthorized.Error()
}

type BadRequestException struct {
	Err string
}

func NewBadRequestException(err string) *BadRequestException {
	return &BadRequestException{
		Err: err,
	}
}

func (e *BadRequestException) Error() string {
	return e.Err
}

type RecordNotFoundException struct {
	ModelName string
}

func NewRecordNotFoundException(modelName string) *RecordNotFoundException {
	return &RecordNotFoundException{
		ModelName: modelName,
	}
}

func (e *RecordNotFoundException) Error() string {
	return ErrNotFound.Error()
}

type UnprocessableEntityException struct {
	Err string
}

func NewErrUnprocessableEntityException(err string) *UnprocessableEntityException {
	return &UnprocessableEntityException{
		Err: err,
	}
}

func (e *UnprocessableEntityException) Error() string {
	return e.Err
}
