package domains

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
)

// Response represents the standard API response structure
// @Description Standard API response structure for both success and error cases
type Response struct {
	// Data contains the response payload for successful requests
	Data interface{} `json:"data,omitempty" swaggertype:"object"`
	// Meta contains metadata about the response (pagination info, messages, etc.)
	Meta interface{} `json:"meta,omitempty" swaggertype:"object"`
	// Errors contains error details when the request fails
	Errors interface{} `json:"errors,omitempty" swaggertype:"array,object"`
}

// PaginationMeta represents the standard pagination metadata
// @Description Pagination metadata structure
type PaginationMeta struct {
	// Current page number
	Page int `json:"page" example:"1"`
	// Number of items per page
	Limit int `json:"limit" example:"10"`
	// Total number of items
	Total int64 `json:"total" example:"100"`
	// Total number of pages
	TotalPage int64 `json:"total_page" example:"10"`
}

// ErrorDetail represents a single error detail
// @Description Error detail structure
type ErrorDetail struct {
	// Error message
	Error string `json:"error" example:"Invalid input"`
}

// SuccessResponse represents a successful API response
type SuccessResponse struct {
	Data interface{} `json:"data"`
	Meta interface{} `json:"meta"`
}

// ErrorResponse represents an error API response
type ErrorResponse struct {
	Meta   interface{} `json:"meta"`
	Errors interface{} `json:"errors"`
}

func writeResponse(w http.ResponseWriter, status int, contentType string, r interface{}) {
	response, err := json.Marshal(r)
	if err != nil {
		slog.ErrorContext(context.Background(), "error while marshaling the response",
			slog.Any("error", err),
			slog.Int("status", status),
			slog.String("content_type", contentType),
			slog.Any("response", r),
		)
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	_, err = w.Write(response)
	if err != nil {
		slog.ErrorContext(context.Background(), "error while writing the response",
			slog.Any("error", err),
			slog.Int("status", status),
			slog.String("content_type", contentType),
			slog.Any("response", r),
		)
	}
}

func WriteSuccessResponse(w http.ResponseWriter, status int, data interface{}, meta interface{}) {
	writeResponse(w, status, "application/json", SuccessResponse{
		Data: data,
		Meta: meta,
	})
}

func WriteSuccessResponseMessage(w http.ResponseWriter, status int, message string) {
	writeResponse(w, status, "application/json", SuccessResponse{
		Data: nil,
		Meta: struct {
			Message string `json:"message"`
		}{
			Message: message,
		},
	})
}

func WriteErrorResponse(w http.ResponseWriter, status int, errors interface{}, meta interface{}) {
	writeResponse(w, status, "application/json", ErrorResponse{
		Meta:   meta,
		Errors: errors,
	})
}

func WriteErrorResponseMessage(w http.ResponseWriter, status int, errors interface{}, message string) {
	writeResponse(w, status, "application/json", ErrorResponse{
		Errors: errors,
		Meta: struct {
			Message string `json:"message"`
		}{
			Message: message,
		},
	})
}

func InternalServerError(w http.ResponseWriter, err string) {
	meta := map[string]string{"message": ErrInternalServerError.Error()}
	errors := []map[string]string{
		{"error": err},
	}

	WriteErrorResponse(w, http.StatusInternalServerError, errors, meta)
}

func RecordNotFound(w http.ResponseWriter, modelName string) {
	meta := map[string]string{"message": ErrNotFound.Error()}
	errors := []map[string]string{
		{"error": fmt.Sprintf("%s not found", modelName)},
	}

	WriteErrorResponse(w, http.StatusNotFound, errors, meta)
}

func BadRequest(w http.ResponseWriter, errors interface{}) {
	meta := map[string]string{"message": ErrBadRequest.Error()}
	WriteErrorResponse(w, http.StatusBadRequest, errors, meta)
}

func Unauthorized(w http.ResponseWriter) {
	meta := map[string]string{"message": ErrUnauthorized.Error()}
	errors := []map[string]string{
		{"error": "You are unauthorized"},
	}

	WriteErrorResponse(w, http.StatusUnauthorized, errors, meta)
}

func SessionExpired(w http.ResponseWriter) {
	meta := map[string]string{"message": ErrUnauthorized.Error()}
	errors := []map[string]string{
		{"error": "Your session has expired"},
	}

	WriteErrorResponse(w, http.StatusUnauthorized, errors, meta)
}

func UnprocessableEntityError(w http.ResponseWriter, err string) {
	meta := map[string]string{"message": ErrUnprocessableEntity.Error()}
	errors := []map[string]string{
		{"error": err},
	}

	WriteErrorResponse(w, http.StatusUnprocessableEntity, errors, meta)
}

func Error(w http.ResponseWriter, err error) {
	switch e := err.(type) {
	case *InternalErrorException:
		InternalServerError(w, e.Error())
	case *UnauthorizedException:
		Unauthorized(w)
	case *BadRequestException:
		BadRequest(w, []map[string]string{{"error": err.Error()}})
	case *RecordNotFoundException:
		RecordNotFound(w, e.ModelName)
	case *UnprocessableEntityException:
		UnprocessableEntityError(w, e.Error())
	default:
		InternalServerError(w, e.Error())
	}
}
