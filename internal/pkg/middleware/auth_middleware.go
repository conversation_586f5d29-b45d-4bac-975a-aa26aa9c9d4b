package middleware

import (
	"context"
	"net/http"

	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
)

func CoreAuthMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ssoID := r.Header.Get("X-Authenticated-Userid")

			if ssoID == "" {
				httpLib.Unauthorized(w)
				return
			}

			ctx := context.WithValue(r.Context(), "ssoID", ssoID)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
