package middleware

import (
	"crypto/subtle"
	"net/http"

	"bitbucket.org/terbang-ventures/notification-service/config"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
)

func InternalAuthMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ApiKey := r.Header.Get("X-Api-Key")
			if ApiKey == "" {
				httpLib.Unauthorized(w)
				return
			}

			staticApiKey := config.GetInternalAPIKey()
			if subtle.ConstantTimeCompare([]byte(ApiKey), []byte(staticApiKey)) != 1 {
				httpLib.Unauthorized(w)
				return
			}

			ctx := r.Context()
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
