package notification

import (
	"encoding/json"
	"fmt"
)

// PayloadData represents the parsed data from FCM payload
type PayloadData struct {
	ActorName   string
	RoomName    string
	ChannelName string
	EventType   string
}

// ParseFCMPayload parses the FCM payload and extracts notification data
func ParseFCMPayload(data map[string]interface{}) (*PayloadData, error) {
	// Check if this payload has the expected structure for automatic notification creation
	eventType, err := extractEventType(data)
	if err != nil {
		return nil, fmt.Errorf("failed to extract event_type: %w", err)
	}

	// Extract data string from payload[:data][:data]
	dataStr, err := extractDataString(data)
	if err != nil {
		return nil, fmt.Errorf("failed to extract data string: %w", err)
	}

	// Parse the data string as JSON
	var parsedData map[string]interface{}
	if err := json.Unmarshal([]byte(dataStr), &parsedData); err != nil {
		return nil, fmt.Errorf("failed to parse data JSON: %w", err)
	}

	// Extract actor_name
	actorName, err := extractActorName(parsedData)
	if err != nil {
		return nil, fmt.Errorf("failed to extract actor_name: %w", err)
	}

	// Extract room_name and channel_name from extra.room
	roomName, channelName, err := extractRoomInfo(parsedData)
	if err != nil {
		return nil, fmt.Errorf("failed to extract room info: %w", err)
	}

	return &PayloadData{
		ActorName:   actorName,
		RoomName:    roomName,
		ChannelName: channelName,
		EventType:   eventType,
	}, nil
}

// IsNotificationPayload checks if the payload contains the required fields for automatic notification creation
func IsNotificationPayload(data map[string]interface{}) bool {
	// Check if event_type exists
	if _, ok := data["event_type"]; !ok {
		return false
	}

	// Check if data field exists
	if _, ok := data["data"]; !ok {
		return false
	}

	return true
}

// BuildDescription builds the notification description based on the parsed data
func (p *PayloadData) BuildDescription() string {
	return fmt.Sprintf("%s assigned you %s on %s", p.ActorName, p.RoomName, p.ChannelName)
}

// TODO: Make it flexible later, along with the description
func (p *PayloadData) BuildTitle() string {
	return "New Notification"
}

// extractEventType extracts event_type from payload[:data][:event_type]
func extractEventType(data map[string]interface{}) (string, error) {
	eventType, ok := data["event_type"]
	if !ok {
		return "", fmt.Errorf("event_type not found in payload data")
	}

	eventTypeStr, ok := eventType.(string)
	if !ok {
		return "", fmt.Errorf("event_type is not a string")
	}

	return eventTypeStr, nil
}

// extractDataString extracts data string from payload[:data][:data]
func extractDataString(data map[string]interface{}) (string, error) {
	dataField, ok := data["data"]
	if !ok {
		return "", fmt.Errorf("data field not found in payload data")
	}

	dataStr, ok := dataField.(string)
	if !ok {
		return "", fmt.Errorf("data field is not a string")
	}

	return dataStr, nil
}

// extractActorName extracts actor_name from parsed data
func extractActorName(parsedData map[string]interface{}) (string, error) {
	actorName, ok := parsedData["actor_name"]
	if !ok {
		return "", fmt.Errorf("actor_name not found in parsed data")
	}

	actorNameStr, ok := actorName.(string)
	if !ok {
		return "", fmt.Errorf("actor_name is not a string")
	}

	return actorNameStr, nil
}

// extractRoomInfo extracts room name and channel from extra.room
func extractRoomInfo(parsedData map[string]interface{}) (string, string, error) {
	extra, ok := parsedData["extra"]
	if !ok {
		return "", "", fmt.Errorf("extra not found in parsed data")
	}

	extraMap, ok := extra.(map[string]interface{})
	if !ok {
		return "", "", fmt.Errorf("extra is not a map")
	}

	room, ok := extraMap["room"]
	if !ok {
		return "", "", fmt.Errorf("room not found in extra")
	}

	roomMap, ok := room.(map[string]interface{})
	if !ok {
		return "", "", fmt.Errorf("room is not a map")
	}

	// Extract room name
	roomName, ok := roomMap["name"]
	if !ok {
		return "", "", fmt.Errorf("room name not found")
	}

	roomNameStr, ok := roomName.(string)
	if !ok {
		return "", "", fmt.Errorf("room name is not a string")
	}

	// Extract channel
	channel, ok := roomMap["channel"]
	if !ok {
		return "", "", fmt.Errorf("channel not found in room")
	}

	channelStr, ok := channel.(string)
	if !ok {
		return "", "", fmt.Errorf("channel is not a string")
	}

	return roomNameStr, channelStr, nil
}
