package mock_repository

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// MockNotificationRepository is a mock implementation of repository.NotificationRepository
type MockNotificationRepository struct {
	mock.Mock
}

// Create mocks the Create method
func (m *MockNotificationRepository) Create(ctx context.Context, notification *model.Notification) error {
	args := m.Called(ctx, notification)
	return args.Error(0)
}

// Create<PERSON>atch mocks the CreateBatch method
func (m *MockNotificationRepository) CreateBatch(ctx context.Context, notifications []*model.Notification) error {
	args := m.Called(ctx, notifications)
	return args.Error(0)
}

// FindByID mocks the FindByID method
func (m *MockNotificationRepository) FindByID(ctx context.Context, id uuid.UUID) (*model.Notification, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Notification), args.Error(1)
}

// FindByIDAndSSO mocks the FindByIDAndSSO method
func (m *MockNotificationRepository) FindByIDAndSSO(ctx context.Context, id uuid.UUID, ssoID uuid.UUID) (*model.Notification, error) {
	args := m.Called(ctx, id, ssoID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Notification), args.Error(1)
}

// FindBySSO mocks the FindBySSO method
func (m *MockNotificationRepository) FindBySSO(ctx context.Context, ssoID uuid.UUID, page, limit int) ([]model.Notification, int64, error) {
	args := m.Called(ctx, ssoID, page, limit)
	return args.Get(0).([]model.Notification), args.Get(1).(int64), args.Error(2)
}

// FindAll mocks the FindAll method
func (m *MockNotificationRepository) FindAll(ctx context.Context, filter *repository.NotificationRepoFilter, page, limit int) ([]model.Notification, int64, error) {
	args := m.Called(ctx, filter, page, limit)
	return args.Get(0).([]model.Notification), args.Get(1).(int64), args.Error(2)
}

// MarkAsRead mocks the MarkAsRead method
func (m *MockNotificationRepository) MarkAsRead(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// MarkAllAsReadBySSO mocks the MarkAllAsReadBySSO method
func (m *MockNotificationRepository) MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID, filter *repository.NotificationRepoFilter) (int64, error) {
	args := m.Called(ctx, ssoID, filter)
	return args.Get(0).(int64), args.Error(1)
}

// CountUnreadBySSO mocks the CountUnreadBySSO method
func (m *MockNotificationRepository) CountUnreadBySSO(ctx context.Context, ssoID uuid.UUID, filter *repository.NotificationRepoFilter) (int64, error) {
	args := m.Called(ctx, ssoID, filter)
	return args.Get(0).(int64), args.Error(0)
}

// Transaction mocks the Transaction method
func (m *MockNotificationRepository) Transaction(fn func(tx *gorm.DB) interface{}) interface{} {
	args := m.Called(fn)
	return args.Get(0)
}
