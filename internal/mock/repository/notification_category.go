package mock_repository

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
)

// MockNotificationCategoryRepository is a mock implementation of repository.NotificationCategoryRepository
type MockNotificationCategoryRepository struct {
	mock.Mock
}

func (m *MockNotificationCategoryRepository) Create(ctx context.Context, notificationCategory *model.NotificationCategory) error {
	args := m.Called(ctx, notificationCategory)
	return args.Error(0)
}

func (m *MockNotificationCategoryRepository) CreateBatch(ctx context.Context, notificationCategories []*model.NotificationCategory) error {
	args := m.Called(ctx, notificationCategories)
	return args.Error(0)
}

func (m *MockNotificationCategoryRepository) FindByID(ctx context.Context, id uuid.UUID) (*model.NotificationCategory, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NotificationCategory), args.Error(1)
}

func (m *MockNotificationCategoryRepository) FindByOriginAndValue(ctx context.Context, origin model.Origin, value string) (*model.NotificationCategory, error) {
	args := m.Called(ctx, origin, value)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NotificationCategory), args.Error(1)
}

func (m *MockNotificationCategoryRepository) FindAll(ctx context.Context) ([]model.NotificationCategory, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.NotificationCategory), args.Error(1)
}

func (m *MockNotificationCategoryRepository) Update(ctx context.Context, notificationCategory *model.NotificationCategory) error {
	args := m.Called(ctx, notificationCategory)
	return args.Error(0)
}

func (m *MockNotificationCategoryRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}
