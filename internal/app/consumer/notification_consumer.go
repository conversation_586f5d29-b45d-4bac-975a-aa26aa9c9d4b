package consumer

import (
	"context"
	"encoding/json"
	"log/slog"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	"github.com/IBM/sarama"
	"github.com/google/uuid"
)

// NotificationMessage represents the message structure for notification creation
type NotificationMessage struct {
	SSOID          uuid.UUID   `json:"sso_id"`
	Title          string      `json:"title"`
	Description    string      `json:"description"`
	ClickAction    string      `json:"click_action"`
	ClickActionURL string      `json:"click_action_url"`
	IsReminder     bool        `json:"is_reminder"`
	NotifType      string      `json:"notif_type_id"`
	NotifCategory  string      `json:"notif_category_id"`
	Origin         string      `json:"origin"`
	Extra          interface{} `json:"extra"`
	OrganizationID *uuid.UUID  `json:"organization_id"`
	EventID        *uuid.UUID  `json:"event_id"`
	EventType      string      `json:"event_type"`
}

// NotificationConsumer handles notification messages from Kafka
type NotificationConsumer struct {
	notificationService service.NotificationService
}

// NewNotificationConsumer creates a new notification consumer
func NewNotificationConsumer(notificationService service.NotificationService) *NotificationConsumer {
	return &NotificationConsumer{
		notificationService: notificationService,
	}
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (c *NotificationConsumer) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (c *NotificationConsumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// HandleMessage processes a notification message
func (c *NotificationConsumer) HandleMessage(ctx context.Context, messageValue []byte) error {
	var message NotificationMessage
	if err := json.Unmarshal(messageValue, &message); err != nil {
		slog.ErrorContext(ctx, "Error unmarshalling notification message",
			slog.Any("error", err),
			slog.String("message", string(messageValue)),
		)
		return err
	}

	// Convert to notification model input
	notificationCreateInput := &service.NotificationCreateInput{
		SSOID:          message.SSOID,
		Title:          message.Title,
		Description:    message.Description,
		ClickAction:    model.ClickAction(message.ClickAction),
		ClickActionURL: message.ClickActionURL,
		IsReminder:     message.IsReminder,
		NotifType:      message.NotifType,
		NotifCategory:  message.NotifCategory,
		Origin:         message.Origin,
		Extra:          message.Extra,
		OrganizationID: message.OrganizationID,
		EventID:        message.EventID,
		EventType:      message.EventType,
	}

	// Create the notification
	_, err := c.notificationService.CreateNotification(ctx, notificationCreateInput)
	if err != nil {
		slog.ErrorContext(ctx, "Error creating notification",
			slog.Any("error", err),
			slog.Any("sso_id", message.SSOID),
		)
		return err
	}

	slog.InfoContext(ctx, "Notification created successfully",
		slog.Any("sso_id", message.SSOID),
		slog.String("description", message.Description),
	)
	return nil
}
