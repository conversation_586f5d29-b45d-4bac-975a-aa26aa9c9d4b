package consumer

import (
	"context"
	"encoding/json"
	"log/slog"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	"github.com/google/uuid"
)

// FCMNotificationMessage represents the message structure for FCM notifications
type FCMNotificationMessage struct {
	SSOIDs       []uuid.UUID            `json:"sso_ids"`
	Data         map[string]interface{} `json:"data"`
	Notification map[string]string      `json:"notification"`
	ClickAction  string                 `json:"click_action"`
}

// FCMConsumer is a Kafka consumer for FCM notifications
type FCMConsumer struct {
	pushNotificationService service.PushNotificationService
}

// NewFCMConsumer creates a new FCM consumer
func NewFCMConsumer(pushNotificationService service.PushNotificationService) *FCMConsumer {
	return &FCMConsumer{
		pushNotificationService: pushNotificationService,
	}
}

// HandleMessage processes an FCM notification message
func (c *FCMConsumer) HandleMessage(ctx context.Context, messageValue []byte) error {
	var message FCMNotificationMessage
	if err := json.Unmarshal(messageValue, &message); err != nil {
		slog.ErrorContext(ctx, "Error unmarshalling FCM notification message",
			slog.Any("error", err),
			slog.String("message", string(messageValue)),
		)
		return err
	}

	// Initialize data if not provided
	if message.Data == nil {
		message.Data = make(map[string]interface{})
	}

	// Send the push notification
	err := c.pushNotificationService.SendPushNotification(
		ctx,
		message.SSOIDs,
		message.Data,
		message.Notification,
		message.ClickAction,
	)
	if err != nil {
		slog.ErrorContext(ctx, "Error sending push notification",
			slog.Any("error", err),
			slog.Any("sso_ids", message.SSOIDs),
		)
		return err
	}

	slog.InfoContext(ctx, "Push notification sent successfully",
		slog.Any("sso_ids", message.SSOIDs),
	)
	return nil
}
