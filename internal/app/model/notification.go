package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Notification represents a notification in the system
type Notification struct {
	ID              uuid.UUID             `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	SSOID           uuid.UUID             `json:"sso_id" gorm:"type:uuid;not null;column:sso_id"`
	Title           string                `json:"title" gorm:"type:text;not null"`
	Description     string                `json:"description" gorm:"type:text;not null"`
	TitleAlt        *string               `json:"title_alt" gorm:"type:text"`
	DescriptionAlt  *string               `json:"description_alt" gorm:"type:text"`
	ClickAction     ClickAction           `json:"click_action" gorm:"type:varchar(50)"`
	ClickActionURL  string                `json:"click_action_url" gorm:"type:text"`
	ReadAt          *time.Time            `json:"read_at" gorm:"type:timestamp"`
	IsR<PERSON>inder      bool                  `json:"is_reminder" gorm:"type:boolean;default:false"`
	NotifTypeID     string                `json:"notif_type_id" gorm:"type:text"`
	NotifCategoryID *uuid.UUID            `json:"notif_category_id" gorm:"type:uuid"`
	NotifCategory   *NotificationCategory `gorm:"foreignKey:NotifCategoryID"`
	CreatedAt       time.Time             `json:"created_at" gorm:"type:timestamp;not null;default:now()"`
	UpdatedAt       time.Time             `json:"updated_at" gorm:"type:timestamp;not null;default:now()"`
	Origin          string                `json:"origin" gorm:"type:varchar(50);check:origin IN ('chat', 'crm')"`
	Extra           datatypes.JSON        `json:"extra" gorm:"type:jsonb"`
	OrganizationID  *uuid.UUID            `json:"organization_id" gorm:"type:uuid"`
	EventID         *uuid.UUID            `json:"event_id" gorm:"type:uuid"`
	EventType       string                `json:"event_type" gorm:"type:text"`
}

// TableName specifies the table name for the Notification model
func (Notification) TableName() string {
	return "notification_banks"
}

// BeforeCreate is a hook that runs before creating a new notification
func (n *Notification) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	n.CreatedAt = time.Now().UTC()
	n.UpdatedAt = time.Now().UTC()
	return nil
}

// BeforeUpdate is a hook that runs before updating a notification
func (n *Notification) BeforeUpdate(tx *gorm.DB) error {
	n.UpdatedAt = time.Now().UTC()
	return nil
}

// NotificationResponse represents the response format for notifications
type NotificationResponse struct {
	ID             uuid.UUID   `json:"id"`
	Description    string      `json:"description"`
	ClickAction    ClickAction `json:"click_action"`
	ClickActionURL string      `json:"click_action_url"`
	IsRead         bool        `json:"is_read"`
	IsReminder     bool        `json:"is_reminder"`
	NotifType      string      `json:"notif_type"`
	NotifCategory  string      `json:"notif_category"`
	CreatedAt      time.Time   `json:"created_at"`
	Origin         string      `json:"origin"`
	Extra          interface{} `json:"extra"`
	OrganizationID *uuid.UUID  `json:"organization_id"`
	EventID        *uuid.UUID  `json:"event_id"`
}

// ToResponse converts a Notification to NotificationResponse
// func (n *Notification) ToResponse() NotificationResponse {
// 	return NotificationResponse{
// 		ID:              n.ID,
// 		Description:     n.Description,
// 		ClickAction:     n.ClickAction,
// 		ClickActionURL:  n.ClickActionURL,
// 		IsRead:          n.ReadAt != nil,
// 		IsReminder:      n.IsReminder,
// 		NotifTypeID:     n.NotifTypeID,
// 		NotifCategoryID: n.NotifCategoryID,
// 		CreatedAt:       n.CreatedAt,
// 		Origin:          n.Origin,
// 		Extra:           n.Extra,
// 		OrganizationID:  n.OrganizationID,
// 		EventID:         n.EventID,
// 	}
// }
