package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserType represents the type of user
type UserType string

const (
	UserTypeAdmin      UserType = "admin"
	UserTypeSupervisor UserType = "supervisor"
	UserTypeAgent      UserType = "agent"
)

// UserSource represents the source of the user
type UserSource string

const (
	UserSourceCRM             UserSource = "crm"
	UserSourceChat            UserSource = "chat"
	UserSourceNotificationSvc UserSource = "notification_service"
)

// FCMToken represents a Firebase Cloud Messaging token for a user
type FCMToken struct {
	ID         uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	SSOID      uuid.UUID  `json:"sso_id" gorm:"type:uuid;not null;index"`
	Token      string     `json:"token" gorm:"type:text;not null;index"`
	UserType   UserType   `json:"user_type" gorm:"type:varchar(50);not null"`
	UserSource UserSource `json:"user_source" gorm:"type:varchar(50);not null"`
	CreatedAt  time.Time  `json:"created_at" gorm:"type:timestamp;not null;default:now()"`
	UpdatedAt  time.Time  `json:"updated_at" gorm:"type:timestamp;not null;default:now()"`
}

// TableName specifies the table name for the FCMToken model
func (FCMToken) TableName() string {
	return "fcm_tokens"
}

// BeforeCreate is a hook that runs before creating a new FCM token
func (t *FCMToken) BeforeCreate(tx *gorm.DB) error {
	if t.ID == uuid.Nil {
		t.ID = uuid.New()
	}
	t.CreatedAt = time.Now().UTC()
	t.UpdatedAt = time.Now().UTC()
	return nil
}

// BeforeUpdate is a hook that runs before updating an FCM token
func (t *FCMToken) BeforeUpdate(tx *gorm.DB) error {
	t.UpdatedAt = time.Now().UTC()
	return nil
}
