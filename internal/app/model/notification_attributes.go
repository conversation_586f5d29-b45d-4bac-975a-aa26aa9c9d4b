package model

import "time"

// ClickAction represents the possible actions when a notification is clicked
type ClickAction string

// ReadStatus represents the whether a notification has been read or not
type ReadStatus string

const (
	// Define possible click actions
	ClickActionOpenURL ClickAction = "OPEN_URL"
	ClickActionOpenApp ClickAction = "OPEN_APP"

	// Define possible read statuses
	ReadStatusRead   ReadStatus = "read"
	ReadStatusUnread ReadStatus = "unread"
)

// Origin represents the origin of the notification type
type Origin string

const (
	OriginChat Origin = "chat"
	OriginCRM  Origin = "crm"
)

// NotificationType represent type of notification, like General, Approval, or Inbox
type NotificationType string

const (
	NotificationTypeGeneral  NotificationType = "1"
	NotificationTypeApproval NotificationType = "2"
	NotificationTypeInbox    NotificationType = "3"
)

// Get date threshold for each Notification Type
func (t NotificationType) GetDateTimeThreshold() *time.Time {
	var threshold *time.Time = nil
	if t == NotificationTypeGeneral {
		typeThreshold := GeneralPeriod.ThresholdDateTime()
		threshold = &typeThreshold
	} else if t == NotificationTypeApproval {
		typeThreshold := ApprovalPeriod.ThresholdDateTime()
		threshold = &typeThreshold
	} else if t == NotificationTypeInbox {
		typeThreshold := InboxPeriod.ThresholdDateTime()
		threshold = &typeThreshold
	}

	return threshold
}

// NotificationRetentionPeriod represents the retention period
type NotificationRetentionPeriod time.Duration

func (p NotificationRetentionPeriod) ThresholdDateTime() time.Time {
	return time.Now().UTC().Add(-time.Duration(p))
}

const (
	// CRM Notifications
	GeneralPeriod  NotificationRetentionPeriod = NotificationRetentionPeriod(90 * 24 * time.Hour) // 90 Days
	ApprovalPeriod NotificationRetentionPeriod = NotificationRetentionPeriod(90 * 24 * time.Hour) // 90 Days

	// Chat Notification
	InboxPeriod NotificationRetentionPeriod = NotificationRetentionPeriod(7 * 24 * time.Hour) // 7 Days
)
