package handler

import (
	"encoding/json"
	"log/slog"
	"net/http"
	"strconv"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// NotificationHandler defines the interface for notification handler operations
type NotificationHandler interface {
	CreateNotification(w http.ResponseWriter, r *http.Request)
	CreateBatchNotification(w http.ResponseWriter, r *http.Request)
	CreateChatNotification(w http.ResponseWriter, r *http.Request)
	CreateCRMNotification(w http.ResponseWriter, r *http.Request)
	GetNotificationByID(w http.ResponseWriter, r *http.Request)
	GetNotifications(w http.ResponseWriter, r *http.Request)
	MarkAsRead(w http.ResponseWriter, r *http.Request)
	MarkAllAsRead(w http.ResponseWriter, r *http.Request)
	CountUnread(w http.ResponseWriter, r *http.Request)
}

type notificationHandler struct {
	notificationService service.NotificationService
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(notificationService service.NotificationService) NotificationHandler {
	return &notificationHandler{
		notificationService: notificationService,
	}
}

// CreateNotificationRequest represents the request body for creating a notification
type CreateNotificationRequest struct {
	SSOID          string                 `json:"sso_id" example:"493fc4e6-14b9-4633-95d0-86b2e27c2387" description:"Target User SSO ID"`
	Title          string                 `json:"title" validate:"required" example:"New Message" description:"Title of the notification"`
	Description    string                 `json:"description" validate:"required" example:"You have a new message" description:"Content of the notification"`
	ClickAction    model.ClickAction      `json:"click_action" example:"OPEN_URL" description:"Action to be performed when notification is clicked"`
	ClickActionURL string                 `json:"click_action_url" example:"https://example.com/message/123" description:"URL to open when notification is clicked"`
	IsReminder     bool                   `json:"is_reminder" example:"false" description:"Whether this notification is a reminder"`
	NotifType      string                 `json:"notif_type" example:"type_123" description:"Type of notification"`
	NotifCategory  string                 `json:"notif_category" example:"category_123" description:"Category of notification"`
	Origin         string                 `json:"origin" example:"chat" description:"Origin of the notification (chat/crm)"`
	Extra          map[string]interface{} `json:"extra" description:"Additional data in JSON format"`
	OrganizationID *uuid.UUID             `json:"organization_id" example:"123e4567-e89b-12d3-a456-************" description:"Organization ID (optional)"`
	EventID        *uuid.UUID             `json:"event_id" example:"123e4567-e89b-12d3-a456-************" description:"Event ID (optional)"`
	EventType      string                 `json:"event_type" example:"add_agent" description:"Event Type (optional)"`
}

// AppCreateNotificationRequest represents the request body for creating a notification for each app
type AppCreateNotificationRequest struct {
	SSOID          string                 `json:"sso_id" example:"493fc4e6-14b9-4633-95d0-86b2e27c2387" description:"Target User SSO ID"`
	Title          string                 `json:"title" validate:"required" example:"New Message" description:"Title of the notification"`
	Description    string                 `json:"description" validate:"required" example:"You have a new message" description:"Content of the notification"`
	ClickAction    model.ClickAction      `json:"click_action" example:"OPEN_URL" description:"Action to be performed when notification is clicked"`
	ClickActionURL string                 `json:"click_action_url" example:"https://example.com/message/123" description:"URL to open when notification is clicked"`
	IsReminder     bool                   `json:"is_reminder" example:"false" description:"Whether this notification is a reminder"`
	NotifType      string                 `json:"notif_type" example:"type_123" description:"Type of notification"`
	NotifCategory  string                 `json:"notif_category" example:"category_123" description:"Category of notification"`
	Extra          map[string]interface{} `json:"extra" description:"Additional data in JSON format"`
	OrganizationID *uuid.UUID             `json:"organization_id" example:"123e4567-e89b-12d3-a456-************" description:"Organization ID (optional)"`
	EventID        *uuid.UUID             `json:"event_id" example:"123e4567-e89b-12d3-a456-************" description:"Event ID (optional)"`
	EventType      string                 `json:"event_type" example:"add_agent" description:"Event Type (optional)"`
}

// @Summary Create a new notification
// @Description Create a new notification for a specific user
// @Tags notifications
// @Deprecated
// @Accept json
// @Produce json
// @Param body body CreateNotificationRequest true "Notification details"
// @Success 201 {object} domains.Response{data=service.NotificationView} "Notification created successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid request body"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications [post]
func (h *notificationHandler) CreateNotification(w http.ResponseWriter, r *http.Request) {
	var req CreateNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid request body"}})
		return
	}

	// Handle target user SSO id
	if req.SSOID == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID not found in context"}})
	}
	ssoID, err := uuid.Parse(req.SSOID)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	notificationCreateInput := &service.NotificationCreateInput{
		SSOID:          ssoID,
		Title:          req.Title,
		Description:    req.Description,
		ClickAction:    req.ClickAction,
		ClickActionURL: req.ClickActionURL,
		IsReminder:     req.IsReminder,
		NotifType:      req.NotifType,
		NotifCategory:  req.NotifCategory,
		Origin:         req.Origin,
		Extra:          req.Extra,
		OrganizationID: req.OrganizationID,
		EventID:        req.EventID,
		EventType:      req.EventType,
	}

	response, err := h.notificationService.CreateNotification(r.Context(), notificationCreateInput)
	if err != nil {
		slog.Error("failed to create new notification",
			slog.String("error", err.Error()),
		)
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusCreated, response, nil)
}

// @Summary Create a new notification (for Chat app)
// @Description Create a new notification for a specific user
// @Tags notifications
// @Accept json
// @Produce json
// @Param X-Api-Key header string true "Internal Notification Service API Key"
// @Param body body AppCreateNotificationRequest true "Notification details"
// @Success 201 {object} domains.Response{data=service.NotificationView} "Notification created successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid request body"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications/chat [post]
func (h *notificationHandler) CreateChatNotification(w http.ResponseWriter, r *http.Request) {
	var req AppCreateNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		slog.Error("failed to create new notification",
			slog.String("error", err.Error()),
		)
		// Create notification endpoint will be consumed by another service. It is safe to return the error message as is.
		// Although, we still need to improve the error message later
		httpLib.BadRequest(w, []map[string]string{{"error": err.Error()}})
		return
	}

	// Handle target user SSO id
	if req.SSOID == "" {
		slog.Error("failed to create new notification",
			slog.String("error", "SSO ID is required"),
		)
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID is required"}})
		return
	}
	ssoID, err := uuid.Parse(req.SSOID)
	if err != nil {
		slog.Error("failed to create new notification",
			slog.String("error", "invalid SSO ID format"),
		)
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	// Validate Chat Notification Type
	if req.NotifType != "3" {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid Chat notification type"}})
		return
	}

	notificationCreateInput := &service.NotificationCreateInput{
		SSOID:          ssoID,
		Title:          req.Title,
		Description:    req.Description,
		ClickAction:    req.ClickAction,
		ClickActionURL: req.ClickActionURL,
		IsReminder:     req.IsReminder,
		NotifType:      req.NotifType,
		NotifCategory:  req.NotifCategory,
		Origin:         "chat", // Notification for Chat endpoint. Enforce origin to be Chat
		Extra:          req.Extra,
		OrganizationID: req.OrganizationID,
		EventID:        req.EventID,
		EventType:      req.EventType,
	}

	response, err := h.notificationService.CreateNotification(r.Context(), notificationCreateInput)
	if err != nil {
		slog.Error("failed to create new notification",
			slog.String("error", err.Error()),
		)
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusCreated, response, nil)
}

// @Summary Create a new notification (for CRM app)
// @Description Create a new notification for a specific user
// @Tags notifications
// @Accept json
// @Produce json
// @Param X-Api-Key header string true "Internal Notification Service API Key"
// @Param body body AppCreateNotificationRequest true "Notification details"
// @Success 201 {object} domains.Response{data=service.NotificationView} "Notification created successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid request body"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications/crm [post]
func (h *notificationHandler) CreateCRMNotification(w http.ResponseWriter, r *http.Request) {
	var req AppCreateNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		slog.Error("failed to create new notification",
			slog.String("error", err.Error()),
		)
		// Create notification endpoint will be consumed by another service. It is safe to return the error message as is.
		// Although, we still need to improve the error message later
		httpLib.BadRequest(w, []map[string]string{{"error": err.Error()}})
		return
	}

	// Handle target user SSO id
	if req.SSOID == "" {
		slog.Error("failed to create new notification",
			slog.String("error", "SSO ID is required"),
		)
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID is required"}})
		return
	}
	ssoID, err := uuid.Parse(req.SSOID)
	if err != nil {
		slog.Error("failed to create new notification",
			slog.String("error", "invalid SSO ID format"),
		)
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	// Validate CRM Notification Type
	if req.NotifType != "1" && req.NotifType != "2" {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid CRM notification type"}})
		return
	}

	notificationCreateInput := &service.NotificationCreateInput{
		SSOID:          ssoID,
		Title:          req.Title,
		Description:    req.Description,
		ClickAction:    req.ClickAction,
		ClickActionURL: req.ClickActionURL,
		IsReminder:     req.IsReminder,
		NotifType:      req.NotifType,
		NotifCategory:  req.NotifCategory,
		Origin:         "crm", // Notification for CRM endpoint. Enforce origin to be CRM
		Extra:          req.Extra,
		OrganizationID: req.OrganizationID,
		EventID:        req.EventID,
		EventType:      req.EventType,
	}

	response, err := h.notificationService.CreateNotification(r.Context(), notificationCreateInput)
	if err != nil {
		slog.Error("failed to create new notification",
			slog.String("error", err.Error()),
		)
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusCreated, response, nil)
}

// Still for internal usage, currently is not exposed.
func (h *notificationHandler) CreateBatchNotification(w http.ResponseWriter, r *http.Request) {
	ssoIDStr, ok := r.Context().Value("ssoID").(string)
	if !ok || ssoIDStr == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID not found in context"}})
		return
	}

	ssoID, err := uuid.Parse(ssoIDStr)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	var reqs []CreateNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&reqs); err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid request body"}})
		return
	}

	var notificationCreateInputs []*service.NotificationCreateInput

	for _, req := range reqs {
		notificationCreateInput := service.NotificationCreateInput{
			SSOID:          ssoID,
			Title:          req.Title,
			Description:    req.Description,
			ClickAction:    req.ClickAction,
			ClickActionURL: req.ClickActionURL,
			IsReminder:     req.IsReminder,
			NotifType:      req.NotifType,
			NotifCategory:  req.NotifCategory,
			Origin:         req.Origin,
			Extra:          req.Extra,
			OrganizationID: req.OrganizationID,
			EventID:        req.EventID,
			EventType:      req.EventType,
		}

		notificationCreateInputs = append(notificationCreateInputs, &notificationCreateInput)
	}

	response, err := h.notificationService.CreateNotificationBatch(r.Context(), notificationCreateInputs)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusCreated, response, nil)
}

// @Summary Get notification by ID
// @Description Retrieve a specific notification by its ID (per user)
// @Tags notifications
// @Produce json
// @Param X-Authenticated-Userid header string true "User SSO ID"
// @Param id path string true "Notification ID (UUID)" format(uuid)
// @Success 200 {object} domains.Response{data=service.NotificationView} "Notification details"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid notification ID"
// @Failure 404 {object} domains.Response{errors=[]domains.ErrorDetail} "Notification not found"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications/{id} [get]
func (h *notificationHandler) GetNotificationByID(w http.ResponseWriter, r *http.Request) {
	// Get SSO ID from middleware
	ssoIDStr, ok := r.Context().Value("ssoID").(string)
	if !ok || ssoIDStr == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID not found in context"}})
		return
	}

	ssoID, err := uuid.Parse(ssoIDStr)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	idParam := chi.URLParam(r, "id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid notification ID"}})
		return
	}

	notification, err := h.notificationService.GetUserNotificationByID(r.Context(), id, ssoID)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, notification, nil)
}

// @Summary Get all notifications
// @Description Retrieve a paginated list of notifications for the authenticated user
// @Tags notifications
// @Produce json
// @Param X-Authenticated-Userid header string true "User SSO ID"
// @Param page query integer false "Page number (default: 1)" minimum(1)
// @Param limit query integer false "Number of items per page (default: 10, max: 100)" minimum(1) maximum(100)
// @Param origin query string false "Notification origin app" example(crm/chat)
// @Param status query string false "Notification read/unread status" example(read/unread)
// @Param notif_type query string false "Type of notification (per origin app)" example(type_1)
// @Param notif_category query string false "Category of notification (per origin app)" example(category_1)
// @Param time_offset query integer false "Time offset (in hour) based on your timezone" minimum(-14) maximum(14)
// @Param language query string false "Language for title and description (default: en)" example(en/id)
// @Success 200 {object} domains.Response{data=[]service.NotificationView,meta=domains.PaginationMeta} "List of notifications with pagination metadata"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications [get]
func (h *notificationHandler) GetNotifications(w http.ResponseWriter, r *http.Request) {
	// Get SSO ID from middleware
	ssoIDStr, ok := r.Context().Value("ssoID").(string)
	if !ok || ssoIDStr == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID not found in context"}})
		return
	}

	ssoID, err := uuid.Parse(ssoIDStr)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	// Validate params
	errors := h.validateParams(r)
	if errors != nil {
		httpLib.BadRequest(w, errors)
	}

	// Parse pagination parameters
	var timeOffset int = 0
	origin := r.URL.Query().Get("origin")
	readStatus := r.URL.Query().Get("status")
	notifCategory := r.URL.Query().Get("notif_category")
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")
	language := r.URL.Query().Get("language")

	// Default language to "en" if not provided or invalid
	if language != "en" && language != "id" {
		language = "en"
	}

	timeOffsetStr := r.URL.Query().Get("time_offset")
	timeOffsetInt, err := strconv.Atoi(timeOffsetStr)
	if err == nil {
		timeOffset = timeOffsetInt
	}

	notifTypeStr := r.URL.Query().Get("notif_type")

	page := 1
	limit := 10

	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	queryData := service.NotificationFilter{
		SSOID:         ssoID.String(),
		Origin:        origin,
		ReadStatus:    readStatus,
		NotifType:     notifTypeStr,
		NotifCategory: notifCategory,
		TimeOffset:    timeOffset,
		Language:      language,
	}

	notifications, total, err := h.notificationService.GetNotificationsBySSO(r.Context(), queryData, page, limit)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	// Create pagination metadata
	meta := map[string]interface{}{
		"page":       page,
		"limit":      limit,
		"total":      total,
		"total_page": (total + int64(limit) - 1) / int64(limit),
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, notifications, meta)
}

// @Summary Mark notification as read
// @Description Mark a specific notification as read
// @Tags notifications
// @Produce json
// @Param X-Authenticated-Userid header string true "User SSO ID"
// @Param id path string true "Notification ID (UUID)" format(uuid)
// @Success 200 {object} domains.Response{message=string} "Notification marked as read successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid notification ID"
// @Failure 404 {object} domains.Response{errors=[]domains.ErrorDetail} "Notification not found"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications/mark_as_read/{id} [put]
func (h *notificationHandler) MarkAsRead(w http.ResponseWriter, r *http.Request) {
	idParam := chi.URLParam(r, "id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid notification ID"}})
		return
	}

	if err := h.notificationService.MarkAsRead(r.Context(), id); err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponseMessage(w, http.StatusOK, "Notification marked as read successfully")
}

// @Summary Mark all notifications as read
// @Description Mark all notifications as read for the authenticated user
// @Tags notifications
// @Produce json
// @Param X-Authenticated-Userid header string true "User SSO ID"
// @Param notif_type query string false "Type of notification (per origin app)" example(type_1)
// @Param time_offset query integer false "Time offset (in hour) based on your timezone" minimum(-14) maximum(14)
// @Success 200 {object} domains.Response{message=string} "All notifications marked as read successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid SSO ID"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications/mark_all_as_read [put]
func (h *notificationHandler) MarkAllAsRead(w http.ResponseWriter, r *http.Request) {
	// Get SSO ID from middleware
	ssoIDStr, ok := r.Context().Value("ssoID").(string)
	if !ok || ssoIDStr == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID not found in context"}})
		return
	}

	ssoID, err := uuid.Parse(ssoIDStr)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	// Validate params
	errors := h.validateParams(r)
	if errors != nil {
		httpLib.BadRequest(w, errors)
	}

	var timeOffset int = 0
	timeOffsetStr := r.URL.Query().Get("time_offset")
	timeOffsetInt, err := strconv.Atoi(timeOffsetStr)
	if err == nil {
		timeOffset = timeOffsetInt
	}

	notifTypeStr := r.URL.Query().Get("notif_type")
	notifCategory := r.URL.Query().Get("notif_category")

	queryData := service.NotificationFilter{
		NotifType:     notifTypeStr,
		NotifCategory: notifCategory,
		TimeOffset:    timeOffset,
	}

	rowsAffected, err := h.notificationService.MarkAllAsReadBySSO(r.Context(), ssoID, queryData)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	returnedCount := map[string]interface{}{"count": rowsAffected}

	httpLib.WriteSuccessResponse(w, http.StatusOK, returnedCount, nil)
}

// CountUnreadResponse represents the response body for unread notification count
type CountUnreadResponse struct {
	Count int64 `json:"count" example:"5"`
}

// @Summary Count unread notifications
// @Description Get the count of unread notifications for the authenticated user
// @Tags notifications
// @Produce json
// @Param X-Authenticated-Userid header string true "User SSO ID"
// @Param notif_type query string false "Type of notification (per origin app)" example(type_1)
// @Param time_offset query integer false "Time offset (in hour) based on your timezone" minimum(-14) maximum(14)
// @Success 200 {object} domains.Response{data=handler.CountUnreadResponse} "Count of unread notifications"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid SSO ID"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications/unread/count [get]
func (h *notificationHandler) CountUnread(w http.ResponseWriter, r *http.Request) {
	// Get SSO ID from middleware
	ssoIDStr, ok := r.Context().Value("ssoID").(string)
	if !ok || ssoIDStr == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID not found in context"}})
		return
	}

	ssoID, err := uuid.Parse(ssoIDStr)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	// Validate params
	errors := h.validateParams(r)
	if errors != nil {
		httpLib.BadRequest(w, errors)
	}

	var timeOffset int = 0
	timeOffsetStr := r.URL.Query().Get("time_offset")
	timeOffsetInt, err := strconv.Atoi(timeOffsetStr)
	if err == nil {
		timeOffset = timeOffsetInt
	}

	notifTypeStr := r.URL.Query().Get("notif_type")

	queryData := service.NotificationFilter{
		NotifType:  notifTypeStr,
		TimeOffset: timeOffset,
	}

	count, err := h.notificationService.CountUnreadBySSO(r.Context(), ssoID, queryData)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	response := CountUnreadResponse{
		Count: count,
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, response, nil)
}

func (h *notificationHandler) validateParams(r *http.Request) interface{} {
	// Notification Type
	notifTypeStr := r.URL.Query().Get("notif_type")
	if notifTypeStr != "" && notifTypeStr != string(model.NotificationTypeGeneral) && notifTypeStr != string(model.NotificationTypeApproval) && notifTypeStr != string(model.NotificationTypeInbox) {
		return []map[string]string{{"error": "Invalid Notification Type"}}
	}

	// Notif Category
	// Caanot stand-alone, type must be provided
	notifCategoryStr := r.URL.Query().Get("notif_category")
	if notifCategoryStr != "" && notifTypeStr == "" {
		return []map[string]string{{"error": "must provide notification type while using category filter"}}
	}

	// Time Offset
	timeOffsetStr := r.URL.Query().Get("time_offset")
	if timeOffsetStr != "" {
		timeOffsetInt, err := strconv.Atoi(timeOffsetStr)
		if err != nil {
			return []map[string]string{{"error": "Invalid time offset"}}
		}
		if timeOffsetInt < -14 || timeOffsetInt > 14 {
			return []map[string]string{{"error": "Invalid time offset"}}
		}
	}

	return nil
}
