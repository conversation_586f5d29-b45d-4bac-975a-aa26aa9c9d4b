package handler

import (
	"encoding/json"
	"net/http"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// FCMTokenHandler defines the interface for FCM token handler operations
type FCMTokenHandler interface {
	RegisterToken(w http.ResponseWriter, r *http.Request)
	GetTokensBySSO(w http.ResponseWriter, r *http.Request)
	DeleteToken(w http.ResponseWriter, r *http.Request)
}

type fcmTokenHandler struct {
	fcmTokenService service.FCMTokenService
}

// NewFCMTokenHandler creates a new FCM token handler
func NewFCMTokenHandler(fcmTokenService service.FCMTokenService) FCMTokenHandler {
	return &fcmTokenHandler{
		fcmTokenService: fcmTokenService,
	}
}

// RegisterTokenRequest represents the request body for registering an FCM token
type RegisterTokenRequest struct {
	SSOID      uuid.UUID        `json:"sso_id" validate:"required" example:"123e4567-e89b-12d3-a456-************" description:"User's SSO ID"`
	Token      string           `json:"token" validate:"required" example:"fcm_token_string" description:"Firebase Cloud Messaging token"`
	UserType   model.UserType   `json:"user_type" validate:"required" example:"customer" description:"Type of user (customer, admin, etc.)"`
	UserSource model.UserSource `json:"user_source" validate:"required" example:"mobile_app" description:"Source of the user (mobile_app, web_app, etc.)"`
}

// @Summary Register FCM token
// @Description Register a new Firebase Cloud Messaging token for a user
// @Tags fcm-tokens
// @Accept json
// @Produce json
// @Param body body RegisterTokenRequest true "FCM token registration details"
// @Success 201 {object} domains.Response{data=model.FCMToken} "Token registered successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid request body or missing required fields"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/fcm-tokens [post]
func (h *fcmTokenHandler) RegisterToken(w http.ResponseWriter, r *http.Request) {
	var req RegisterTokenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid request body"}})
		return
	}

	// Validate required fields
	if req.SSOID == uuid.Nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID is required"}})
		return
	}
	if req.Token == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "Token is required"}})
		return
	}
	if req.UserType == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "User type is required"}})
		return
	}
	if req.UserSource == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "User source is required"}})
		return
	}

	token := &model.FCMToken{
		SSOID:      req.SSOID,
		Token:      req.Token,
		UserType:   req.UserType,
		UserSource: req.UserSource,
	}

	if err := h.fcmTokenService.RegisterToken(r.Context(), token); err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusCreated, token, nil)
}

// @Summary Get FCM tokens by SSO ID
// @Description Retrieve all FCM tokens associated with a specific SSO ID
// @Tags fcm-tokens
// @Produce json
// @Param sso_id path string true "SSO ID (UUID)" format(uuid)
// @Success 200 {object} domains.Response{data=[]model.FCMToken} "List of FCM tokens"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid SSO ID"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/fcm-tokens/sso/{sso_id} [get]
func (h *fcmTokenHandler) GetTokensBySSO(w http.ResponseWriter, r *http.Request) {
	idParam := chi.URLParam(r, "sso_id")
	ssoID, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID"}})
		return
	}

	tokens, err := h.fcmTokenService.GetTokensBySSO(r.Context(), ssoID)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, tokens, nil)
}

// @Summary Delete FCM token
// @Description Delete a specific FCM token by its ID
// @Tags fcm-tokens
// @Produce json
// @Param id path string true "Token ID (UUID)" format(uuid)
// @Success 200 {object} domains.Response{message=string} "Token deleted successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid token ID"
// @Failure 404 {object} domains.Response{errors=[]domains.ErrorDetail} "Token not found"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/fcm-tokens/{id} [delete]
func (h *fcmTokenHandler) DeleteToken(w http.ResponseWriter, r *http.Request) {
	idParam := chi.URLParam(r, "id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid token ID"}})
		return
	}

	if err := h.fcmTokenService.DeleteToken(r.Context(), id); err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponseMessage(w, http.StatusOK, "Token deleted successfully")
}
