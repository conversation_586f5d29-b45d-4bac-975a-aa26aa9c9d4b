package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// MockNotificationService is a mock implementation of service.NotificationService
type MockNotificationService struct {
	mock.Mock
}

// CreateNotification mocks the CreateNotification method
func (m *MockNotificationService) CreateNotification(ctx context.Context, notification *service.NotificationCreateInput) (*service.NotificationView, error) {
	args := m.Called(ctx, notification)
	return args.Get(0).(*service.NotificationView), args.Error(1)
}

// CreateNotificationBatch mocks the CreateNotificationBatch method
func (m *MockNotificationService) CreateNotificationBatch(ctx context.Context, notifications []*service.NotificationCreateInput) ([]*service.NotificationView, error) {
	args := m.Called(ctx, notifications)
	return args.Get(0).([]*service.NotificationView), args.Error(1)
}

// GetNotificationByID mocks the GetNotificationByID method
func (m *MockNotificationService) GetNotificationByID(ctx context.Context, id uuid.UUID) (*service.NotificationView, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*service.NotificationView), args.Error(1)
}

// GetUserNotificationByID mocks the GetUserNotificationByID method
func (m *MockNotificationService) GetUserNotificationByID(ctx context.Context, id uuid.UUID, ssoID uuid.UUID) (*service.NotificationView, error) {
	args := m.Called(ctx, id, ssoID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*service.NotificationView), args.Error(1)
}

// GetNotificationsBySSO mocks the GetNotificationsBySSO method
func (m *MockNotificationService) GetNotificationsBySSO(ctx context.Context, filter service.NotificationFilter, page, limit int) ([]service.NotificationView, int64, error) {
	args := m.Called(ctx, filter, page, limit)
	return args.Get(0).([]service.NotificationView), args.Get(1).(int64), args.Error(2)
}

// GetNotifications mocks the GetNotifications method
func (m *MockNotificationService) GetNotifications(ctx context.Context, page, limit int) ([]service.NotificationView, int64, error) {
	args := m.Called(ctx, page, limit)
	return args.Get(0).([]service.NotificationView), args.Get(1).(int64), args.Error(2)
}

// MarkAsRead mocks the MarkAsRead method
func (m *MockNotificationService) MarkAsRead(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// MarkAllAsReadBySSO mocks the MarkAllAsReadBySSO method
func (m *MockNotificationService) MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID, filter service.NotificationFilter) (int64, error) {
	args := m.Called(ctx, ssoID, filter)
	return args.Get(0).(int64), args.Error(0)
}

// MarkAllAsReadBySSO mocks the MarkAllAsReadBySSO method
func (m *MockNotificationService) CountUnreadBySSO(ctx context.Context, ssoID uuid.UUID, filter service.NotificationFilter) (int64, error) {
	args := m.Called(ctx, ssoID, filter)
	return args.Get(0).(int64), args.Error(1)
}

// NotificationHandlerTestSuite is a test suite for NotificationHandler
type NotificationHandlerTestSuite struct {
	suite.Suite
	mockService          *MockNotificationService
	handler              NotificationHandler
	notification         *model.Notification
	notificationCategory *model.NotificationCategory
	notificationView     *service.NotificationView
	ctx                  context.Context
}

// SetupTest sets up the test suite
func (suite *NotificationHandlerTestSuite) SetupTest() {
	suite.mockService = new(MockNotificationService)
	suite.handler = NewNotificationHandler(suite.mockService)
	orgID := uuid.New()
	eventID := uuid.New()
	notifCategoryID := uuid.New()
	suite.notificationCategory = &model.NotificationCategory{
		ID:          notifCategoryID,
		Origin:      "chat",
		OriginValue: "cat_1",
	}
	suite.notification = &model.Notification{
		ID:              uuid.New(),
		SSOID:           uuid.New(),
		Title:           "Test Title",
		Description:     "Test notification",
		ClickAction:     model.ClickActionOpenURL,
		ClickActionURL:  "https://example.com",
		IsReminder:      false,
		NotifTypeID:     "1",
		NotifCategoryID: &notifCategoryID,
		Origin:          "chat",
		OrganizationID:  &orgID,
		EventID:         &eventID,
		EventType:       "some_event",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	suite.ctx = context.Background()
	suite.notificationView = &service.NotificationView{
		ID:             suite.notification.ID,
		Title:          suite.notification.Title,
		Description:    suite.notification.Description,
		ClickAction:    suite.notification.ClickAction,
		ClickActionURL: suite.notification.ClickActionURL,
		IsReminder:     suite.notification.IsReminder,
		NotifType:      suite.notification.NotifTypeID,
		NotifCategory:  suite.notificationCategory.OriginValue,
		Origin:         suite.notification.Origin,
		OrganizationID: suite.notification.OrganizationID,
		EventID:        suite.notification.EventID,
		EventType:      suite.notification.EventType,
	}
}

// TestCreateNotification tests the CreateNotification method
func (suite *NotificationHandlerTestSuite) TestCreateNotification() {
	// Setup
	reqBody := CreateNotificationRequest{
		SSOID:          uuid.New().String(),
		Title:          suite.notification.Title,
		Description:    suite.notification.Description,
		ClickAction:    suite.notification.ClickAction,
		ClickActionURL: suite.notification.ClickActionURL,
		IsReminder:     suite.notification.IsReminder,
		NotifType:      suite.notification.NotifTypeID,
		NotifCategory:  suite.notificationCategory.OriginValue,
		Origin:         suite.notification.Origin,
		OrganizationID: suite.notification.OrganizationID,
		EventID:        suite.notification.EventID,
		EventType:      suite.notification.EventType,
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewReader(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	suite.mockService.On("CreateNotification", req.Context(), mock.AnythingOfType("*service.NotificationCreateInput")).
		Run(func(args mock.Arguments) {
			notificationInput := args.Get(1).(*service.NotificationCreateInput)
			notificationInput.SSOID = suite.notification.SSOID
			notificationInput.Title = suite.notification.Title
		}).
		Return(suite.notificationView, nil)

	// Execute
	suite.handler.CreateNotification(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])

	suite.mockService.AssertExpectations(suite.T())
}

// TestCreateNotification_InvalidJSON tests the CreateNotification method with invalid JSON
func (suite *NotificationHandlerTestSuite) TestCreateNotification_InvalidJSON() {
	// Setup
	reqBodyBytes := []byte(`{invalid json}`)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewReader(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Execute
	suite.handler.CreateNotification(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestCreateNotification_ServiceError tests the CreateNotification method with a service error
func (suite *NotificationHandlerTestSuite) TestCreateNotification_ServiceError() {
	// Setup
	reqBody := CreateNotificationRequest{
		SSOID:          uuid.New().String(),
		Title:          suite.notification.Title,
		Description:    suite.notification.Description,
		ClickAction:    suite.notification.ClickAction,
		ClickActionURL: suite.notification.ClickActionURL,
		IsReminder:     suite.notification.IsReminder,
		NotifType:      suite.notification.NotifTypeID,
		NotifCategory:  suite.notificationCategory.OriginValue,
		Origin:         suite.notification.Origin,
		OrganizationID: suite.notification.OrganizationID,
		EventID:        suite.notification.EventID,
		EventType:      suite.notification.EventType,
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewReader(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	var notificationView *service.NotificationView
	expectedError := errors.New("service error")
	suite.mockService.On("CreateNotification", req.Context(), mock.AnythingOfType("*service.NotificationCreateInput")).
		Return(notificationView, expectedError)

	// Execute
	suite.handler.CreateNotification(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestCreateChatNotification tests the CreateChatNotification method
func (suite *NotificationHandlerTestSuite) TestCreateChatNotification() {
	// Setup
	reqBody := AppCreateNotificationRequest{
		SSOID:          uuid.New().String(),
		Title:          suite.notification.Title,
		Description:    suite.notification.Description,
		ClickAction:    suite.notification.ClickAction,
		ClickActionURL: suite.notification.ClickActionURL,
		IsReminder:     suite.notification.IsReminder,
		NotifType:      "3",
		NotifCategory:  suite.notificationCategory.OriginValue,
		OrganizationID: suite.notification.OrganizationID,
		EventID:        suite.notification.EventID,
		EventType:      suite.notification.EventType,
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/notifications/chat", bytes.NewReader(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	suite.mockService.On("CreateNotification", req.Context(), mock.AnythingOfType("*service.NotificationCreateInput")).
		Run(func(args mock.Arguments) {
			notificationInput := args.Get(1).(*service.NotificationCreateInput)
			notificationInput.SSOID = suite.notification.SSOID
			notificationInput.Title = suite.notification.Title
		}).
		Return(suite.notificationView, nil)

	// Execute
	suite.handler.CreateChatNotification(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])

	suite.mockService.AssertExpectations(suite.T())
}

// TestCreateCRMNotification tests the CreateCRMNotification method
func (suite *NotificationHandlerTestSuite) TestCreateCRMNotification() {
	// Setup
	reqBody := AppCreateNotificationRequest{
		SSOID:          uuid.New().String(),
		Title:          suite.notification.Title,
		Description:    suite.notification.Description,
		ClickAction:    suite.notification.ClickAction,
		ClickActionURL: suite.notification.ClickActionURL,
		IsReminder:     suite.notification.IsReminder,
		NotifType:      suite.notification.NotifTypeID,
		NotifCategory:  suite.notificationCategory.OriginValue,
		OrganizationID: suite.notification.OrganizationID,
		EventID:        suite.notification.EventID,
		EventType:      suite.notification.EventType,
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/notifications/crm", bytes.NewReader(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	suite.mockService.On("CreateNotification", req.Context(), mock.AnythingOfType("*service.NotificationCreateInput")).
		Run(func(args mock.Arguments) {
			notificationInput := args.Get(1).(*service.NotificationCreateInput)
			notificationInput.SSOID = suite.notification.SSOID
			notificationInput.Title = suite.notification.Title
		}).
		Return(suite.notificationView, nil)

	// Execute
	suite.handler.CreateCRMNotification(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotificationByID tests the GetNotificationByID method
func (suite *NotificationHandlerTestSuite) TestGetNotificationByID() {
	// Setup
	id := suite.notification.ID
	req := httptest.NewRequest(http.MethodGet, "/notifications/"+id.String(), nil)
	w := httptest.NewRecorder()

	// Create a new router context with URL params & SSO ID
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("id", id.String())
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(context.WithValue(ctx, chi.RouteCtxKey, rctx))

	suite.mockService.On("GetUserNotificationByID", req.Context(), id, suite.notification.SSOID).Return(suite.notificationView, nil)

	// Execute
	suite.handler.GetNotificationByID(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotificationByID_InvalidID tests the GetNotificationByID method with an invalid ID
func (suite *NotificationHandlerTestSuite) TestGetNotificationByID_InvalidID() {
	// Setup
	req := httptest.NewRequest(http.MethodGet, "/notifications/invalid-id", nil)
	w := httptest.NewRecorder()

	// Create a new router context with URL params
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("id", "invalid-id")
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(context.WithValue(ctx, chi.RouteCtxKey, rctx))

	// Execute
	suite.handler.GetNotificationByID(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotificationByID_NotFound tests the GetNotificationByID method with a not found error
func (suite *NotificationHandlerTestSuite) TestGetNotificationByID_NotFound() {
	// Setup
	id := suite.notification.ID
	req := httptest.NewRequest(http.MethodGet, "/notifications/"+id.String(), nil)
	w := httptest.NewRecorder()

	// Create a new router context with URL params
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("id", id.String())
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(context.WithValue(ctx, chi.RouteCtxKey, rctx))

	expectedError := &httpLib.RecordNotFoundException{ModelName: "Notification"}
	suite.mockService.On("GetUserNotificationByID", req.Context(), id, suite.notification.SSOID).Return(nil, expectedError)

	// Execute
	suite.handler.GetNotificationByID(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotificationByID_ServiceError tests the GetNotificationByID method with a service error
func (suite *NotificationHandlerTestSuite) TestGetNotificationByID_ServiceError() {
	// Setup
	id := suite.notification.ID
	req := httptest.NewRequest(http.MethodGet, "/notifications/"+id.String(), nil)
	w := httptest.NewRecorder()

	// Create a new router context with URL params
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("id", id.String())
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(context.WithValue(ctx, chi.RouteCtxKey, rctx))

	expectedError := errors.New("service error")
	suite.mockService.On("GetUserNotificationByID", req.Context(), id, suite.notification.SSOID).Return(nil, expectedError)

	// Execute
	suite.handler.GetNotificationByID(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotifications tests the GetNotifications method
func (suite *NotificationHandlerTestSuite) TestGetNotifications() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 20
	notificationViews := []service.NotificationView{*suite.notificationView}

	req := httptest.NewRequest(http.MethodGet, "/notifications?page=1&limit=10", nil)
	w := httptest.NewRecorder()

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)

	filter := service.NotificationFilter{
		SSOID:         suite.notification.SSOID.String(),
		Origin:        "",
		ReadStatus:    "",
		NotifType:     "",
		NotifCategory: "",
	}
	suite.mockService.On("GetNotificationsBySSO", req.Context(), filter, page, limit).Return(notificationViews, total, nil)

	// Execute
	suite.handler.GetNotifications(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])
	assert.NotNil(suite.T(), response["meta"])

	meta := response["meta"].(map[string]interface{})
	assert.Equal(suite.T(), float64(page), meta["page"])
	assert.Equal(suite.T(), float64(limit), meta["limit"])
	assert.Equal(suite.T(), float64(total), meta["total"])
	assert.Equal(suite.T(), float64(2), meta["total_page"]) // 20 items with 10 per page = 2 pages

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotifications_DefaultParams tests the GetNotifications method with default parameters
func (suite *NotificationHandlerTestSuite) TestGetNotifications_DefaultParams() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 20
	notificationViews := []service.NotificationView{*suite.notificationView}

	req := httptest.NewRequest(http.MethodGet, "/notifications", nil)
	w := httptest.NewRecorder()

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)

	filter := service.NotificationFilter{
		SSOID:         suite.notification.SSOID.String(),
		Origin:        "",
		ReadStatus:    "",
		NotifType:     "",
		NotifCategory: "",
	}
	suite.mockService.On("GetNotificationsBySSO", req.Context(), filter, page, limit).Return(notificationViews, total, nil)

	// Execute
	suite.handler.GetNotifications(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])
	assert.NotNil(suite.T(), response["meta"])

	meta := response["meta"].(map[string]interface{})
	assert.Equal(suite.T(), float64(page), meta["page"])
	assert.Equal(suite.T(), float64(limit), meta["limit"])
	assert.Equal(suite.T(), float64(total), meta["total"])
	assert.Equal(suite.T(), float64(2), meta["total_page"]) // 20 items with 10 per page = 2 pages

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotifications_InvalidParams tests the GetNotifications method with invalid parameters
func (suite *NotificationHandlerTestSuite) TestGetNotifications_InvalidParams() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 20
	notificationViews := []service.NotificationView{*suite.notificationView}

	req := httptest.NewRequest(http.MethodGet, "/notifications?page=invalid&limit=invalid", nil)
	w := httptest.NewRecorder()

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)

	filter := service.NotificationFilter{
		SSOID:         suite.notification.SSOID.String(),
		Origin:        "",
		ReadStatus:    "",
		NotifType:     "",
		NotifCategory: "",
	}
	suite.mockService.On("GetNotificationsBySSO", req.Context(), filter, page, limit).Return(notificationViews, total, nil)

	// Execute
	suite.handler.GetNotifications(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])
	assert.NotNil(suite.T(), response["meta"])

	meta := response["meta"].(map[string]interface{})
	assert.Equal(suite.T(), float64(page), meta["page"])
	assert.Equal(suite.T(), float64(limit), meta["limit"])
	assert.Equal(suite.T(), float64(total), meta["total"])
	assert.Equal(suite.T(), float64(2), meta["total_page"]) // 20 items with 10 per page = 2 pages

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotifications_ServiceError tests the GetNotifications method with a service error
func (suite *NotificationHandlerTestSuite) TestGetNotifications_ServiceError() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 0
	var notificationViews []service.NotificationView

	req := httptest.NewRequest(http.MethodGet, "/notifications", nil)
	w := httptest.NewRecorder()

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)

	filter := service.NotificationFilter{
		SSOID:         suite.notification.SSOID.String(),
		Origin:        "",
		ReadStatus:    "",
		NotifType:     "",
		NotifCategory: "",
	}

	expectedError := errors.New("service error")
	suite.mockService.On("GetNotificationsBySSO", req.Context(), filter, page, limit).Return(notificationViews, total, expectedError)

	// Execute
	suite.handler.GetNotifications(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestNotificationHandlerSuite runs the test suite
func TestNotificationHandlerSuite(t *testing.T) {
	suite.Run(t, new(NotificationHandlerTestSuite))
}
