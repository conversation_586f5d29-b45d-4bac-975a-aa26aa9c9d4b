package handler

import (
	"net/http"

	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
)

// HealthHandler defines the interface for health check operations
type HealthHandler interface {
	HealthCheck(w http.ResponseWriter, r *http.Request)
}

type healthHandler struct{}

// NewHealthHandler creates a new health handler
func NewHealthHandler() HealthHandler {
	return &healthHandler{}
}

// @Summary Health check
// @Description Check if the service is healthy and running
// @Tags health
// @Produce json
// @Success 200 {object} domains.Response{message=string} "Service is healthy"
// @Router /api/v1/health [get]
func (h *healthHandler) HealthCheck(w http.ResponseWriter, r *http.Request) {
	httpLib.WriteSuccessResponseMessage(w, http.StatusOK, "OK")
}
