package repository

import (
	"context"
	"database/sql"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationRepository defines the interface for notification repository operations
type NotificationRepository interface {
	Transaction(fn func(tx *gorm.DB) interface{}) interface{}
	Create(ctx context.Context, notification *model.Notification) error
	CreateBatch(ctx context.Context, notifications []*model.Notification) error
	FindByID(ctx context.Context, id uuid.UUID) (*model.Notification, error)
	FindByIDAndSSO(ctx context.Context, id uuid.UUID, ssoID uuid.UUID) (*model.Notification, error)
	FindBySSO(ctx context.Context, ssoID uuid.UUID, page, limit int) ([]model.Notification, int64, error)
	FindAll(ctx context.Context, filter *NotificationRepoFilter, page, limit int) ([]model.Notification, int64, error)
	MarkAsRead(ctx context.Context, id uuid.UUID) error
	MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID, filter *NotificationRepoFilter) (int64, error)
	CountUnreadBySSO(ctx context.Context, ssoID uuid.UUID, filter *NotificationRepoFilter) (int64, error)
}

type notificationRepository struct {
	db *gorm.DB
}

// NewNotificationRepository creates a new notification repository
func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &notificationRepository{
		db: db,
	}
}

func (r *notificationRepository) handleFilters(db *gorm.DB, filter *NotificationRepoFilter) *gorm.DB {
	// Filters
	if filter.SSOID != "" {
		db = db.Where("notification_banks.sso_id = @sso_id", sql.Named("sso_id", filter.SSOID))
	}
	if filter.Origin != "" {
		db = db.Where("notification_banks.origin ILIKE @origin", sql.Named("origin", filter.Origin))
	}
	if filter.OnlyUnreadStatus != nil {
		switch *filter.OnlyUnreadStatus {
		case true:
			db = db.Where("notification_banks.read_at IS NULL")
		case false:
			db = db.Where("notification_banks.read_at IS NOT NULL")
		}
	}
	if filter.StartDateTime != nil {
		db = db.Where("notification_banks.created_at >= @startDateTime", sql.Named("startDateTime", filter.StartDateTime))
	}

	// Notification Type is Static
	if filter.NotifType != "" {
		db = db.Where("notification_banks.notif_type_id = @notifType", sql.Named("notifType", filter.NotifType))
	}

	// Notification Category requires join
	if filter.NotifCategory != "" {
		db = db.Joins("JOIN notification_categories ON notification_banks.notif_category_id = notification_categories.id").Where("notification_categories.origin_value ILIKE @origin_value", sql.Named("origin_value", filter.NotifCategory))
	}

	return db
}

func (r *notificationRepository) handleJoins(db *gorm.DB) *gorm.DB {
	return db.Preload("NotifCategory")
}

// Create creates a new notification
func (r *notificationRepository) Create(ctx context.Context, notification *model.Notification) error {
	return r.db.WithContext(ctx).Create(notification).Error
}

// CreateBatch creates multiple notifications in a single transaction
func (r *notificationRepository) CreateBatch(ctx context.Context, notifications []*model.Notification) error {
	if len(notifications) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(notifications).Error
}

// FindByID finds a notification by ID
func (r *notificationRepository) FindByID(ctx context.Context, id uuid.UUID) (*model.Notification, error) {
	var notification model.Notification

	db := r.db.WithContext(ctx)
	db = r.handleJoins(r.db)
	err := db.Where("id = ?", id).First(&notification).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

// FindByIDAndSSO finds a notification by ID and User SSO ID
func (r *notificationRepository) FindByIDAndSSO(ctx context.Context, id uuid.UUID, ssoID uuid.UUID) (*model.Notification, error) {
	var notification model.Notification
	db := r.handleJoins(r.db)
	err := db.WithContext(ctx).Where("id = @id", sql.Named("id", id)).Where("sso_id = @sso_id", sql.Named("sso_id", ssoID)).First(&notification).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

// FindBySSO finds notifications by SSO ID with pagination
func (r *notificationRepository) FindBySSO(ctx context.Context, ssoID uuid.UUID, page, limit int) ([]model.Notification, int64, error) {
	var notifications []model.Notification
	var total int64

	// Calculate offset
	offset := (page - 1) * limit

	db := r.handleJoins(r.db)
	// Count total records for this SSO ID

	if err := db.WithContext(ctx).Model(&model.Notification{}).Where("sso_id = ?", ssoID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get records with pagination

	if err := db.WithContext(ctx).Where("sso_id = ?", ssoID).Offset(offset).Limit(limit).Order("created_at DESC").Find(&notifications).Error; err != nil {
		return nil, 0, err
	}

	return notifications, total, nil
}

// FindAll finds all notifications with pagination
func (r *notificationRepository) FindAll(ctx context.Context, filter *NotificationRepoFilter, page, limit int) ([]model.Notification, int64, error) {
	var notifications []model.Notification
	var total int64

	// Calculate offset
	offset := (page - 1) * limit

	db := r.db.WithContext(ctx)
	db = r.handleJoins(r.db)
	db = r.handleFilters(db, filter)

	// Count total records
	if err := db.WithContext(ctx).Model(&model.Notification{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get records with pagination
	if err := db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&notifications).Error; err != nil {
		return nil, 0, err
	}

	return notifications, total, nil
}

// MarkAsRead marks a notification as read
func (r *notificationRepository) MarkAsRead(ctx context.Context, id uuid.UUID) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&model.Notification{}).Where("id = ?", id).Update("read_at", now).Error
}

// MarkAllAsReadBySSO marks all notifications as read for a specific SSO ID
func (r *notificationRepository) MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID, filter *NotificationRepoFilter) (int64, error) {
	now := time.Now()

	var result *gorm.DB
	// We will use raw query if Notification Category is in the filter. GORM does not support update-from query
	if filter.NotifCategory != "" {
		result = r.db.Exec(`UPDATE notification_banks
		 					SET read_at = ? 
							FROM notification_categories
							WHERE notification_categories.id = notification_banks.notif_category_id
							AND notification_banks.sso_id = ?
							AND notification_categories.origin_value = ?
							AND notification_banks.notif_type_id = ?
							AND notification_banks.created_at >= ?
							AND read_at IS NULL`,
			now, ssoID, filter.NotifCategory, filter.NotifType, filter.StartDateTime)

	} else {
		db := r.db.WithContext(ctx).Model(&model.Notification{})
		db = r.handleFilters(db, filter)

		if filter.StartDateTime != nil {
			db = db.Where("notification_banks.created_at >= @startDateTime", sql.Named("startDateTime", filter.StartDateTime))
		}

		result = db.Where("sso_id = ? AND read_at IS NULL", ssoID).Update("read_at", now)
	}

	return result.RowsAffected, result.Error
}

// CountUnreadBySSO counts unread notifications for a specific SSO ID
func (r *notificationRepository) CountUnreadBySSO(ctx context.Context, ssoID uuid.UUID, filter *NotificationRepoFilter) (int64, error) {
	var count int64

	db := r.db.WithContext(ctx)
	db = r.handleFilters(db, filter)

	err := db.Model(&model.Notification{}).Where("sso_id = ? AND read_at IS NULL", ssoID).Count(&count).Error
	return count, err
}

func (r *notificationRepository) Transaction(fn func(tx *gorm.DB) interface{}) interface{} {
	tx := r.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	result := fn(tx)

	// Handle errors explicitly
	if err, ok := result.(error); ok && err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
