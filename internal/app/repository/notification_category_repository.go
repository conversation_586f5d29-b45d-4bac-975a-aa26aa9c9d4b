package repository

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationCategoryRepository defines the interface for notification category repository operations
type NotificationCategoryRepository interface {
	Create(ctx context.Context, notificationCategory *model.NotificationCategory) error
	CreateBatch(ctx context.Context, notificationCategories []*model.NotificationCategory) error
	FindByID(ctx context.Context, id uuid.UUID) (*model.NotificationCategory, error)
	FindByOriginAndValue(ctx context.Context, origin model.Origin, value string) (*model.NotificationCategory, error)
	FindAll(ctx context.Context) ([]model.NotificationCategory, error)
	Update(ctx context.Context, notificationCategory *model.NotificationCategory) error
	Delete(ctx context.Context, id uuid.UUID) error
}

type notificationCategoryRepository struct {
	db *gorm.DB
}

// NewNotificationCategoryRepository creates a new notification category repository
func NewNotificationCategoryRepository(db *gorm.DB) NotificationCategoryRepository {
	return &notificationCategoryRepository{
		db: db,
	}
}

// Create creates a new notification category
func (r *notificationCategoryRepository) Create(ctx context.Context, notificationCategory *model.NotificationCategory) error {
	return r.db.WithContext(ctx).Create(notificationCategory).Error
}

// CreateBatch creates multiple notification categories in a single transaction
func (r *notificationCategoryRepository) CreateBatch(ctx context.Context, notificationCategories []*model.NotificationCategory) error {
	if len(notificationCategories) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(notificationCategories).Error
}

// FindByID finds a notification category by ID
func (r *notificationCategoryRepository) FindByID(ctx context.Context, id uuid.UUID) (*model.NotificationCategory, error) {
	var notificationCategory model.NotificationCategory
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&notificationCategory).Error
	if err != nil {
		return nil, err
	}
	return &notificationCategory, nil
}

// FindByOriginAndValue finds a notification category by origin and value
func (r *notificationCategoryRepository) FindByOriginAndValue(ctx context.Context, origin model.Origin, value string) (*model.NotificationCategory, error) {
	var notificationCategory model.NotificationCategory
	err := r.db.WithContext(ctx).Where("origin = ? AND origin_value = ?", origin, value).First(&notificationCategory).Error
	if err != nil {
		return nil, err
	}
	return &notificationCategory, nil
}

// FindAll finds all notification categories
func (r *notificationCategoryRepository) FindAll(ctx context.Context) ([]model.NotificationCategory, error) {
	var notificationCategories []model.NotificationCategory
	err := r.db.WithContext(ctx).Find(&notificationCategories).Error
	return notificationCategories, err
}

// Update updates a notification category
func (r *notificationCategoryRepository) Update(ctx context.Context, notificationCategory *model.NotificationCategory) error {
	return r.db.WithContext(ctx).Save(notificationCategory).Error
}

// Delete deletes a notification category
func (r *notificationCategoryRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.NotificationCategory{}, id).Error
}
