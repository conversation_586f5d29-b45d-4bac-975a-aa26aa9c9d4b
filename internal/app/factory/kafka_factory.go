package factory

import (
	"crypto/tls"
	"crypto/x509"
	"log/slog"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/config"
	"github.com/IBM/sarama"
)

// CreateKafkaConsumer creates a new Kafka consumer with the application configuration
func CreateKafkaConsumer(groupID string, topics []string) (*config.KafkaConsumer, error) {
	// Get Kafka configuration from environment
	brokers := config.GetKafkaBrokers()
	saslEnabled := config.GetKafkaSasl()
	username := config.GetKafkaUsername()
	password := config.GetKafkaPassword()
	cert := config.GetKafkaCert()
	clientID := config.GetKafkaPrefix() + "-" + groupID

	// Create Sarama config
	kafkaConfig := sarama.NewConfig()
	kafkaConfig.ClientID = clientID
	kafkaConfig.Version = sarama.V2_2_0_0
	kafkaConfig.Consumer.Return.Errors = true
	kafkaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
	kafkaConfig.Metadata.Full = true
	kafkaConfig.Net.KeepAlive = 30 * time.Second

	// Configure TLS if certificate is provided
	if cert != "" {
		kafkaConfig.Net.TLS.Enable = true

		clientCertPool := x509.NewCertPool()
		ok := clientCertPool.AppendCertsFromPEM([]byte(cert))
		if !ok {
			slog.Error("Kafka certificate is invalid")
		}
		kafkaConfig.Net.TLS.Config = &tls.Config{
			RootCAs: clientCertPool,
			/* #nosec */
			InsecureSkipVerify: true,
		}
	}

	// Configure SASL if enabled
	if saslEnabled {
		kafkaConfig.Net.SASL.Enable = true
		kafkaConfig.Net.SASL.User = username
		kafkaConfig.Net.SASL.Password = password
		kafkaConfig.Net.SASL.Handshake = true
	}

	// Create the consumer
	return config.NewKafkaConsumer(brokers, groupID, topics, kafkaConfig)
}
