package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// NotificationService defines the interface for notification service operations
type NotificationService interface {
	CreateNotification(ctx context.Context, notificationCreateInput *NotificationCreateInput) (*NotificationView, error)
	CreateNotificationBatch(ctx context.Context, notificationCreateInputs []*NotificationCreateInput) ([]*NotificationView, error)
	GetNotificationByID(ctx context.Context, id uuid.UUID) (*NotificationView, error)
	GetUserNotificationByID(ctx context.Context, id uuid.UUID, ssoID uuid.UUID) (*NotificationView, error)
	GetNotificationsBySSO(ctx context.Context, filter NotificationFilter, page, limit int) ([]NotificationView, int64, error)
	GetNotifications(ctx context.Context, page, limit int) ([]NotificationView, int64, error)
	MarkAsRead(ctx context.Context, id uuid.UUID) error
	MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID, filter NotificationFilter) (int64, error)
	CountUnreadBySSO(ctx context.Context, ssoID uuid.UUID, filter NotificationFilter) (int64, error)
}

type notificationService struct {
	notificationRepo         repository.NotificationRepository
	notificationCategoryRepo repository.NotificationCategoryRepository
}

// NewNotificationService creates a new notification service
func NewNotificationService(notificationRepo repository.NotificationRepository, notificationCategoryRepo repository.NotificationCategoryRepository) NotificationService {
	return &notificationService{
		notificationRepo:         notificationRepo,
		notificationCategoryRepo: notificationCategoryRepo,
	}
}

// getRetentionStartTime returns the start time for each notification retention period based on notifcation type and time offset.
func (s *notificationService) getRetentionStartTime(notifTypeStr string, timeOffset int) *time.Time {
	// Simulation below, with 1 day retention period.
	// Let's assume we are in 4 Aug 10 AM (Local UTC+7)
	// With conditions as stated, we want to filter notifications starting at 4 Aug 00.00 (Local UTC+7) = 3 Aug 17.00 (UTC)

	// 1 - Get start of retention in UTC [GetDateTimeThreshold]
	// 4 Aug 3 AM (UTC) [time.Now() in UTC]
	// 4 Aug 3 AM (UTC) -> 3 Aug 3 AM (UTC) [Substract by retention period (1 day)]

	// 2 - Applies time offset
	// 3 Aug 3 AM (UTC) + 7 hours (offset) -> 3 Aug 10 AM (UTC+7)

	// 3 - Get beginning day of the next date
	// Beginning of next day is 4 Aug 00.00 AM (UTC+7)

	// 4 - Return to UTC, because we are filtering date in UTC
	// 4 Aug 00.00 AM - 7 hours (offset) -> 3 Aug 17.00 (UTC)

	// Filtering data with: 3 Aug 17.00 PM (UTC)
	// Because it is equal to 4 Aug 00.00 AM (Local), which we care about the user's local time.

	var startDateTime *time.Time

	// 1 - Get start time of a retention based on notification time
	notifType := model.NotificationType(notifTypeStr)
	startDateTime = notifType.GetDateTimeThreshold()
	if startDateTime == nil {
		return nil
	}

	// 2 - Then applies time offset if start date time exists
	*startDateTime = startDateTime.Add(time.Duration(timeOffset) * time.Hour)

	// 3 - Granularity is in date, so we will return the beginning of the next day
	t := startDateTime
	*startDateTime = time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.UTC).Add(24 * time.Hour)

	// 4 - The beginning of the day includes the time offset, we need to set it back to UTC (as we store records in UTC)
	*startDateTime = startDateTime.Add(time.Duration(-1*timeOffset) * time.Hour)

	return startDateTime
}

// CreateNotification creates a new notification
func (s *notificationService) CreateNotification(ctx context.Context, notificationCreateInput *NotificationCreateInput) (*NotificationView, error) {
	// Handle notification category. Create new category if not exists
	var notificationCategory *model.NotificationCategory
	var notifCategoryID *uuid.UUID
	if notificationCreateInput.NotifCategory != "" {
		categoryData, err := s.notificationCategoryRepo.FindByOriginAndValue(ctx, model.Origin(notificationCreateInput.Origin), notificationCreateInput.NotifCategory)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, httpLib.ErrInternalServerError
		}

		notificationCategory = categoryData
		if notificationCategory == nil {
			notificationCategory = &model.NotificationCategory{
				Origin:      model.Origin(notificationCreateInput.Origin),
				Name:        notificationCreateInput.NotifCategory,
				OriginValue: notificationCreateInput.NotifCategory,
			}
			s.notificationCategoryRepo.Create(ctx, notificationCategory)
		}

		notifCategoryID = &notificationCategory.ID
	}

	var jsonExtra datatypes.JSON
	jsonBytes, err := json.Marshal(notificationCreateInput.Extra)
	if err != nil {
		slog.Error("invalid extra data",
			slog.String("sso_id", notificationCreateInput.SSOID.String()),
		)
		return nil, httpLib.ErrBadRequest
	}
	jsonExtra = datatypes.JSON(jsonBytes)

	notification := &model.Notification{
		SSOID:           notificationCreateInput.SSOID,
		Title:           notificationCreateInput.Title,
		Description:     notificationCreateInput.Description,
		ClickAction:     notificationCreateInput.ClickAction,
		ClickActionURL:  notificationCreateInput.ClickActionURL,
		IsReminder:      notificationCreateInput.IsReminder,
		NotifTypeID:     notificationCreateInput.NotifType,
		NotifCategoryID: notifCategoryID,
		Origin:          notificationCreateInput.Origin,
		Extra:           jsonExtra,
		OrganizationID:  notificationCreateInput.OrganizationID,
		EventID:         notificationCreateInput.EventID,
		EventType:       notificationCreateInput.EventType,
	}

	if err := s.notificationRepo.Create(ctx, notification); err != nil {
		return nil, httpLib.NewInternalErrorException(err.Error())
	}

	view := GenerateNotificationView(notification, notificationCategory, 0, "en")
	return view, nil
}

// CreateNotificationBatch creates multiple notifications
func (s *notificationService) CreateNotificationBatch(ctx context.Context, notificationCreateInputs []*NotificationCreateInput) ([]*NotificationView, error) {
	notificationViews := []*NotificationView{}

	err := s.notificationRepo.Transaction(func(tx *gorm.DB) interface{} {
		// Handle notification category
		notificationCategories, err := s.notificationCategoryRepo.FindAll(ctx)
		if err != nil {
			return httpLib.NewInternalErrorException(err.Error())
		}

		// Available notification category mapping
		var categoryMap map[string]*model.NotificationCategory
		categoryMap = map[string]*model.NotificationCategory{}
		for _, notifCategory := range notificationCategories {
			key := fmt.Sprintf("(%s,%s)", notifCategory.Origin, notifCategory.OriginValue)
			if _, ok := categoryMap[key]; !ok {
				categoryMap[key] = &notifCategory
			}
		}

		// Create new categories if not exists
		var newNotificationCategories []*model.NotificationCategory

		for _, notificationInput := range notificationCreateInputs {
			if notificationInput.NotifCategory != "" {
				categoryKey := fmt.Sprintf("(%s,%s)", notificationInput.Origin, notificationInput.NotifCategory)
				if _, ok := categoryMap[categoryKey]; !ok {
					newCategory := model.NotificationCategory{
						Origin:      model.Origin(notificationInput.Origin),
						Name:        notificationInput.NotifCategory,
						OriginValue: notificationInput.NotifCategory,
					}

					categoryMap[categoryKey] = &newCategory
					newNotificationCategories = append(newNotificationCategories, &newCategory)
				}
			}
		}

		if err = s.notificationCategoryRepo.CreateBatch(ctx, newNotificationCategories); err != nil {
			return httpLib.NewInternalErrorException(err.Error())
		}

		var notifications []*model.Notification
		for _, notificationCreateInput := range notificationCreateInputs {
			var notifCategoryID *uuid.UUID
			categoryKey := fmt.Sprintf("(%s,%s)", notificationCreateInput.Origin, notificationCreateInput.NotifCategory)
			if notifCategory, ok := categoryMap[categoryKey]; ok {
				notifCategoryID = &notifCategory.ID
			}

			var jsonExtra datatypes.JSON
			jsonBytes, err := json.Marshal(notificationCreateInput.Extra)
			if err != nil {
				slog.Error("invalid extra data",
					slog.String("sso_id", notificationCreateInput.SSOID.String()),
				)
				return httpLib.ErrBadRequest
			}
			jsonExtra = datatypes.JSON(jsonBytes)

			notification := model.Notification{
				SSOID:           notificationCreateInput.SSOID,
				Title:           notificationCreateInput.Title,
				Description:     notificationCreateInput.Description,
				ClickAction:     notificationCreateInput.ClickAction,
				ClickActionURL:  notificationCreateInput.ClickActionURL,
				IsReminder:      notificationCreateInput.IsReminder,
				NotifTypeID:     notificationCreateInput.NotifType,
				NotifCategoryID: notifCategoryID,
				Origin:          notificationCreateInput.Origin,
				Extra:           jsonExtra,
				OrganizationID:  notificationCreateInput.OrganizationID,
				EventID:         notificationCreateInput.EventID,
				EventType:       notificationCreateInput.EventType,
			}

			notifications = append(notifications, &notification)
		}

		if err = s.notificationRepo.CreateBatch(ctx, notifications); err != nil {
			return httpLib.NewInternalErrorException(err.Error())
		}

		// Order of notifications is maintained
		for idx, notificationCreateInput := range notificationCreateInputs {
			categoryKey := fmt.Sprintf("(%s,%s)", notificationCreateInput.Origin, notificationCreateInput.NotifCategory)

			notification := notifications[idx]
			notifCategory := categoryMap[categoryKey]

			notificationViews = append(notificationViews, GenerateNotificationView(notification, notifCategory, 0, "en"))
		}

		return nil
	})

	if err != nil {
		return nil, err.(error)
	}

	return notificationViews, nil
}

// GetNotificationByID gets a notification by ID
func (s *notificationService) GetNotificationByID(ctx context.Context, id uuid.UUID) (*NotificationView, error) {
	notification, err := s.notificationRepo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, httpLib.NewRecordNotFoundException("Notification")
		}
		return nil, err
	}
	response := GenerateNotificationView(notification, nil, 0, "en")
	return response, nil
}

// GetUserNotificationByID gets a notification by ID and User SSO ID
func (s *notificationService) GetUserNotificationByID(ctx context.Context, id uuid.UUID, ssoID uuid.UUID) (*NotificationView, error) {
	notification, err := s.notificationRepo.FindByIDAndSSO(ctx, id, ssoID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, httpLib.NewRecordNotFoundException("Notification")
		}
		return nil, err
	}
	response := GenerateNotificationView(notification, nil, 0, "en")
	return response, nil
}

// GetNotificationsBySSO gets notifications by SSO ID with pagination
func (s *notificationService) GetNotificationsBySSO(ctx context.Context, filter NotificationFilter, page, limit int) ([]NotificationView, int64, error) {
	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	var onlyUnreadStatus *bool
	switch filter.ReadStatus {
	case string(model.ReadStatusUnread):
		val := true
		onlyUnreadStatus = &val
	case string(model.ReadStatusRead):
		val := false
		onlyUnreadStatus = &val
	}

	var startDateTime *time.Time = nil
	startDateTime = s.getRetentionStartTime(filter.NotifType, filter.TimeOffset)

	repoFilter := repository.NotificationRepoFilter{
		SSOID:            filter.SSOID,
		Origin:           filter.Origin,
		OnlyUnreadStatus: onlyUnreadStatus,
		NotifType:        filter.NotifType,
		NotifCategory:    filter.NotifCategory,
		StartDateTime:    startDateTime,
	}

	notifications, total, err := s.notificationRepo.FindAll(ctx, &repoFilter, page, limit)
	if err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]NotificationView, len(notifications))
	for i, notification := range notifications {
		responses[i] = *GenerateNotificationView(&notification, nil, filter.TimeOffset, filter.Language)
	}

	return responses, total, nil
}

// GetNotifications gets all notifications with pagination
func (s *notificationService) GetNotifications(ctx context.Context, page, limit int) ([]NotificationView, int64, error) {
	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	notifications, total, err := s.notificationRepo.FindAll(ctx, nil, page, limit)
	if err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]NotificationView, len(notifications))
	for i, notification := range notifications {
		responses[i] = *GenerateNotificationView(&notification, nil, 0, "en")
	}

	return responses, total, nil
}

// MarkAsRead marks a notification as read
func (s *notificationService) MarkAsRead(ctx context.Context, id uuid.UUID) error {
	return s.notificationRepo.MarkAsRead(ctx, id)
}

// MarkAllAsReadBySSO marks all notifications as read for a specific SSO ID
func (s *notificationService) MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID, filter NotificationFilter) (int64, error) {
	var startDateTime *time.Time = nil
	startDateTime = s.getRetentionStartTime(filter.NotifType, filter.TimeOffset)

	repoFilter := repository.NotificationRepoFilter{
		Origin:        filter.Origin,
		NotifType:     filter.NotifType,
		NotifCategory: filter.NotifCategory,
		StartDateTime: startDateTime,
	}

	return s.notificationRepo.MarkAllAsReadBySSO(ctx, ssoID, &repoFilter)
}

// CountUnreadBySSO counts unread notifications for a specific SSO ID
func (s *notificationService) CountUnreadBySSO(ctx context.Context, ssoID uuid.UUID, filter NotificationFilter) (int64, error) {
	var startDateTime *time.Time = nil
	startDateTime = s.getRetentionStartTime(filter.NotifType, filter.TimeOffset)

	repoFilter := repository.NotificationRepoFilter{
		Origin:        filter.Origin,
		NotifType:     filter.NotifType,
		StartDateTime: startDateTime,
	}

	return s.notificationRepo.CountUnreadBySSO(ctx, ssoID, &repoFilter)
}
