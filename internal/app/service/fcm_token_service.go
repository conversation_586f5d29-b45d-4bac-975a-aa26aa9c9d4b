package service

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// FCMTokenService defines the interface for FCM token service operations
type FCMTokenService interface {
	RegisterToken(ctx context.Context, token *model.FCMToken) error
	GetTokensBySSO(ctx context.Context, ssoID uuid.UUID) ([]model.FCMToken, error)
	GetTokensBySSOs(ctx context.Context, ssoIDs []uuid.UUID) ([]model.FCMToken, error)
	UpdateToken(ctx context.Context, token *model.FCMToken) error
	DeleteToken(ctx context.Context, id uuid.UUID) error
}

type fcmTokenService struct {
	fcmTokenRepo repository.FCMTokenRepository
}

// NewFCMTokenService creates a new FCM token service
func NewFCMTokenService(fcmTokenRepo repository.FCMTokenRepository) FCMTokenService {
	return &fcmTokenService{
		fcmTokenRepo: fcmTokenRepo,
	}
}

// RegisterToken registers a new FCM token
func (s *fcmTokenService) RegisterToken(ctx context.Context, token *model.FCMToken) error {
	// Check if token already exists
	existingToken, err := s.fcmTokenRepo.FindByToken(ctx, token.Token)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	// If token exists, update it
	if existingToken != nil {
		existingToken.SSOID = token.SSOID
		existingToken.UserType = token.UserType
		existingToken.UserSource = token.UserSource
		return s.fcmTokenRepo.Update(ctx, existingToken)
	}

	// Otherwise, create a new token
	return s.fcmTokenRepo.Create(ctx, token)
}

// GetTokensBySSO gets FCM tokens by SSO ID
func (s *fcmTokenService) GetTokensBySSO(ctx context.Context, ssoID uuid.UUID) ([]model.FCMToken, error) {
	tokens, err := s.fcmTokenRepo.FindBySSO(ctx, ssoID)
	if err != nil {
		return nil, err
	}
	return tokens, nil
}

// GetTokensBySSOs gets FCM tokens by multiple SSO IDs
func (s *fcmTokenService) GetTokensBySSOs(ctx context.Context, ssoIDs []uuid.UUID) ([]model.FCMToken, error) {
	if len(ssoIDs) == 0 {
		return []model.FCMToken{}, nil
	}

	tokens, err := s.fcmTokenRepo.FindBySSOs(ctx, ssoIDs)
	if err != nil {
		return nil, err
	}
	return tokens, nil
}

// UpdateToken updates an FCM token
func (s *fcmTokenService) UpdateToken(ctx context.Context, token *model.FCMToken) error {
	// Check if token exists
	_, err := s.fcmTokenRepo.FindByID(ctx, token.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return httpLib.NewRecordNotFoundException("FCM Token")
		}
		return err
	}

	// Update token
	return s.fcmTokenRepo.Update(ctx, token)
}

// DeleteToken deletes an FCM token
func (s *fcmTokenService) DeleteToken(ctx context.Context, id uuid.UUID) error {
	// Check if token exists
	_, err := s.fcmTokenRepo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return httpLib.NewRecordNotFoundException("FCM Token")
		}
		return err
	}

	// Delete token
	return s.fcmTokenRepo.Delete(ctx, id)
}
