package service

import (
	"context"
	"errors"
	"testing"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	mock_repository "bitbucket.org/terbang-ventures/notification-service/internal/mock/repository"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

// NotificationServiceTestSuite is a test suite for NotificationService
type NotificationServiceTestSuite struct {
	suite.Suite
	mockRepo             *mock_repository.MockNotificationRepository
	mockCategoryRepo     *mock_repository.MockNotificationCategoryRepository
	service              NotificationService
	notification         *model.Notification
	notificationCategory *model.NotificationCategory
	notificationView     *NotificationView
	ctx                  context.Context
}

// SetupTest sets up the test suite
func (suite *NotificationServiceTestSuite) SetupTest() {
	suite.mockRepo = new(mock_repository.MockNotificationRepository)
	suite.mockCategoryRepo = new(mock_repository.MockNotificationCategoryRepository)
	suite.service = NewNotificationService(suite.mockRepo, suite.mockCategoryRepo)
	orgID := uuid.New()
	eventID := uuid.New()
	notifCategoryID := uuid.New()

	suite.notificationCategory = &model.NotificationCategory{
		ID:          notifCategoryID,
		Origin:      "chat",
		OriginValue: "cat_1",
	}
	suite.notification = &model.Notification{
		SSOID:           uuid.New(),
		Title:           "New notification",
		Description:     "Test notification",
		ClickAction:     model.ClickActionOpenURL,
		ClickActionURL:  "https://example.com",
		IsReminder:      false,
		NotifTypeID:     "1",
		NotifCategoryID: &notifCategoryID,
		Origin:          "chat",
		OrganizationID:  &orgID,
		EventID:         &eventID,
		EventType:       "some_event",
	}
	suite.notificationView = &NotificationView{
		ID:             suite.notification.ID,
		Title:          suite.notification.Title,
		Description:    suite.notification.Description,
		ClickAction:    suite.notification.ClickAction,
		ClickActionURL: suite.notification.ClickActionURL,
		IsReminder:     suite.notification.IsReminder,
		NotifType:      suite.notification.NotifTypeID,
		NotifCategory:  suite.notificationCategory.OriginValue,
		Origin:         suite.notification.Origin,
		OrganizationID: suite.notification.OrganizationID,
		EventID:        suite.notification.EventID,
		EventType:      suite.notification.EventType,
	}

	suite.ctx = context.Background()
}

// TestCreateNotification tests the CreateNotification method
func (suite *NotificationServiceTestSuite) TestCreateNotification() {
	// Setup
	suite.mockRepo.On("Create", suite.ctx, suite.notification).Return(nil)
	suite.mockCategoryRepo.On("FindByOriginAndValue", suite.ctx, model.Origin(suite.notification.Origin), suite.notificationCategory.OriginValue).Return(suite.notificationCategory, nil)

	// Execute
	createInput := &NotificationCreateInput{
		SSOID:          suite.notification.SSOID,
		Title:          suite.notification.Title,
		Description:    suite.notification.Description,
		ClickAction:    suite.notification.ClickAction,
		ClickActionURL: suite.notification.ClickActionURL,
		IsReminder:     suite.notification.IsReminder,
		NotifType:      suite.notification.NotifTypeID,
		NotifCategory:  suite.notificationCategory.OriginValue,
		Origin:         suite.notification.Origin,
		Extra:          suite.notification.Extra,
		OrganizationID: suite.notification.OrganizationID,
		EventID:        suite.notification.EventID,
		EventType:      suite.notification.EventType,
	}
	_, err := suite.service.CreateNotification(suite.ctx, createInput)

	// Assert
	assert.NoError(suite.T(), err)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestCreateNotification_Error tests the CreateNotification method with an error
func (suite *NotificationServiceTestSuite) TestCreateNotification_Error() {
	// Setup
	expectedError := gorm.ErrInvalidData
	suite.mockRepo.On("Create", suite.ctx, suite.notification).Return(expectedError)
	suite.mockCategoryRepo.On("FindByOriginAndValue", suite.ctx, model.Origin(suite.notification.Origin), suite.notificationCategory.OriginValue).Return(suite.notificationCategory, nil)

	// Execute
	createInput := &NotificationCreateInput{
		SSOID:          suite.notification.SSOID,
		Title:          suite.notification.Title,
		Description:    suite.notification.Description,
		ClickAction:    suite.notification.ClickAction,
		ClickActionURL: suite.notification.ClickActionURL,
		IsReminder:     suite.notification.IsReminder,
		NotifType:      suite.notification.NotifTypeID,
		NotifCategory:  suite.notificationCategory.OriginValue,
		Origin:         suite.notification.Origin,
		Extra:          suite.notification.Extra,
		OrganizationID: suite.notification.OrganizationID,
		EventID:        suite.notification.EventID,
		EventType:      suite.notification.EventType,
	}
	_, err := suite.service.CreateNotification(suite.ctx, createInput)

	// Assert
	var internalErr *httpLib.InternalErrorException
	ok := errors.As(err, &internalErr)
	assert.True(suite.T(), ok)

	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotificationByID tests the GetNotificationByID method
func (suite *NotificationServiceTestSuite) TestGetNotificationByID() {
	// Setup
	id := uuid.New()
	suite.mockRepo.On("FindByID", suite.ctx, id).Return(suite.notification, nil)

	// Execute
	notification, err := suite.service.GetNotificationByID(suite.ctx, id)

	// Assert
	assert.NoError(suite.T(), err)
	expectedResponse := GenerateNotificationView(suite.notification, nil, 0, "en")
	assert.Equal(suite.T(), expectedResponse, notification)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotificationByID_NotFound tests the GetNotificationByID method with a not found error
func (suite *NotificationServiceTestSuite) TestGetNotificationByID_NotFound() {
	// Setup
	id := uuid.New()
	suite.mockRepo.On("FindByID", suite.ctx, id).Return(nil, gorm.ErrRecordNotFound)

	// Execute
	notification, err := suite.service.GetNotificationByID(suite.ctx, id)

	// Assert
	assert.Error(suite.T(), err)
	assert.IsType(suite.T(), &httpLib.RecordNotFoundException{}, err)
	assert.Nil(suite.T(), notification)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotificationByID_Error tests the GetNotificationByID method with an error
func (suite *NotificationServiceTestSuite) TestGetNotificationByID_Error() {
	// Setup
	id := uuid.New()
	expectedError := gorm.ErrInvalidDB
	suite.mockRepo.On("FindByID", suite.ctx, id).Return(nil, expectedError)

	// Execute
	notification, err := suite.service.GetNotificationByID(suite.ctx, id)

	// Assert
	assert.Equal(suite.T(), expectedError, err)
	assert.Nil(suite.T(), notification)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetUserNotificationByID tests the GetUserNotificationByID method
func (suite *NotificationServiceTestSuite) TestGetUserNotificationByID() {
	// Setup
	id := uuid.New()
	suite.mockRepo.On("FindByIDAndSSO", suite.ctx, id, suite.notification.SSOID).Return(suite.notification, nil)

	// Execute
	notification, err := suite.service.GetUserNotificationByID(suite.ctx, id, suite.notification.SSOID)

	// Assert
	assert.NoError(suite.T(), err)
	expectedResponse := GenerateNotificationView(suite.notification, nil, 0, "en")
	assert.Equal(suite.T(), expectedResponse, notification)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetUserNotificationByID_NotFound tests the GetUserNotificationByID method with a not found error
func (suite *NotificationServiceTestSuite) TestGetUserNotificationByID_NotFound() {
	// Setup
	id := uuid.New()
	suite.mockRepo.On("FindByIDAndSSO", suite.ctx, id, suite.notification.SSOID).Return(nil, gorm.ErrRecordNotFound)

	// Execute
	notification, err := suite.service.GetUserNotificationByID(suite.ctx, id, suite.notification.SSOID)

	// Assert
	assert.Error(suite.T(), err)
	assert.IsType(suite.T(), &httpLib.RecordNotFoundException{}, err)
	assert.Nil(suite.T(), notification)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications tests the GetNotifications method
func (suite *NotificationServiceTestSuite) TestGetNotifications() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}
	suite.mockRepo.On("FindAll", suite.ctx, mock.AnythingOfType("*repository.NotificationRepoFilter"), page, limit).Return(notifications, total, nil)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.NoError(suite.T(), err)
	expectedResponses := []NotificationView{*GenerateNotificationView(suite.notification, nil, 0, "en")}
	assert.Equal(suite.T(), expectedResponses, result)
	assert.Equal(suite.T(), total, count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications_InvalidPage tests the GetNotifications method with an invalid page
func (suite *NotificationServiceTestSuite) TestGetNotifications_InvalidPage() {
	// Setup
	page := 0
	limit := 10
	expectedPage := 1
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}
	suite.mockRepo.On("FindAll", suite.ctx, mock.AnythingOfType("*repository.NotificationRepoFilter"), expectedPage, limit).Return(notifications, total, nil)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.NoError(suite.T(), err)
	expectedResponses := []NotificationView{*GenerateNotificationView(suite.notification, nil, 0, "en")}
	assert.Equal(suite.T(), expectedResponses, result)
	assert.Equal(suite.T(), total, count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications_InvalidLimit tests the GetNotifications method with an invalid limit
func (suite *NotificationServiceTestSuite) TestGetNotifications_InvalidLimit() {
	// Setup
	page := 1
	limit := 0
	expectedLimit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}
	suite.mockRepo.On("FindAll", suite.ctx, mock.AnythingOfType("*repository.NotificationRepoFilter"), page, expectedLimit).Return(notifications, total, nil)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.NoError(suite.T(), err)
	expectedResponses := []NotificationView{*GenerateNotificationView(suite.notification, nil, 0, "en")}
	assert.Equal(suite.T(), expectedResponses, result)
	assert.Equal(suite.T(), total, count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications_LargeLimit tests the GetNotifications method with a large limit
func (suite *NotificationServiceTestSuite) TestGetNotifications_LargeLimit() {
	// Setup
	page := 1
	limit := 200
	expectedLimit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}
	suite.mockRepo.On("FindAll", suite.ctx, mock.AnythingOfType("*repository.NotificationRepoFilter"), page, expectedLimit).Return(notifications, total, nil)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.NoError(suite.T(), err)
	expectedResponses := []NotificationView{*GenerateNotificationView(suite.notification, nil, 0, "en")}
	assert.Equal(suite.T(), expectedResponses, result)
	assert.Equal(suite.T(), total, count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications_Error tests the GetNotifications method with an error
func (suite *NotificationServiceTestSuite) TestGetNotifications_Error() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 0
	expectedError := gorm.ErrInvalidDB
	var notifications []model.Notification
	suite.mockRepo.On("FindAll", suite.ctx, mock.AnythingOfType("*repository.NotificationRepoFilter"), page, limit).Return(notifications, total, expectedError)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.Equal(suite.T(), expectedError, err)
	assert.Empty(suite.T(), result)
	assert.Equal(suite.T(), int64(0), count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestNotificationServiceSuite runs the test suite
func TestNotificationServiceSuite(t *testing.T) {
	suite.Run(t, new(NotificationServiceTestSuite))
}
