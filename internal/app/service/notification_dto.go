package service

import (
	"encoding/json"
	"fmt"
	"math"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"github.com/google/uuid"
)

// Input to create new notification
type NotificationCreateInput struct {
	SSOID          uuid.UUID
	Title          string
	Description    string
	ClickAction    model.ClickAction
	ClickActionURL string
	IsReminder     bool
	NotifType      string
	NotifCategory  string
	Origin         string
	Extra          interface{}
	OrganizationID *uuid.UUID
	EventID        *uuid.UUID
	EventType      string
}

// Input to filter notifications
type NotificationFilter struct {
	SSOID         string
	Origin        string
	ReadStatus    string
	NotifType     string
	NotifCategory string
	TimeOffset    int
	Language      string
}

// Output to see notifications
type NotificationView struct {
	ID             uuid.UUID         `json:"id" example:"f77f7e12-a72f-4e67-b359-512c7e5415bc"`
	Title          string            `json:"title" example:"New Chat Assignment"`
	Description    string            `json:"description" example:"Supervisor has assigned you to a Chat"`
	ClickAction    model.ClickAction `json:"click_action" example:"OPEN_NEW_CHAT"`
	ClickActionURL string            `json:"click_action_url" example:"https://staging-chat.qontak.com/inbox/123"`
	IsReminder     bool              `json:"is_reminder" example:"false"`
	CreatedAt      string            `json:"created_at" example:"2025-08-04T06:23:15+07:00"`
	ReadAt         string            `json:"read_at" example:"2025-08-04T18:55:41+07:00"`
	NotifType      string            `json:"notif_type" example:"3"`
	NotifCategory  string            `json:"notif_category" example:"1"`
	Origin         string            `json:"origin" example:"chat"`
	Extra          interface{}       `json:"extra"`
	OrganizationID *uuid.UUID        `json:"organization_id" example:"123e4567-e89b-12d3-a456-************"`
	EventID        *uuid.UUID        `json:"event_id" example:"123e4567-e89b-12d3-a456-************"`
	EventType      string            `json:"event_type" example:"EVENT_NAME"`
}

func convertToTimeOffset(target time.Time, offset int) time.Time {
	if offset < 0 {
		locationTz := time.FixedZone(fmt.Sprintf("UTC-%d", math.Abs(float64(offset))), offset*60*60)
		return target.In(locationTz)
	} else if offset > 0 {
		locationTz := time.FixedZone(fmt.Sprintf("UTC+%d", offset), offset*60*60)
		return target.In(locationTz)
	}

	return target
}

func GenerateNotificationView(notification *model.Notification, notificationCategory *model.NotificationCategory, timeOffset int, language string) *NotificationView {
	var notifCategoryValue string
	if notification.NotifCategory != nil {
		notifCategoryValue = notification.NotifCategory.OriginValue
	} else if notificationCategory != nil {
		notifCategoryValue = notificationCategory.OriginValue
	}

	// CreatedAt and ReadAt are datetime in the form of RFC 3339
	createdAt := convertToTimeOffset(notification.CreatedAt, timeOffset).Format(time.RFC3339)
	readAt := ""
	if notification.ReadAt != nil {
		readAt = convertToTimeOffset(*notification.ReadAt, timeOffset).Format(time.RFC3339)
	}

	var jsonExtra map[string]interface{} = map[string]interface{}{}
	_ = json.Unmarshal(notification.Extra, &jsonExtra)

	// Handle language-specific title and description
	title := notification.Title
	description := notification.Description

	// If language is Indonesian and alternative text exists, use it
	if language == "id" {
		if notification.TitleAlt != nil && *notification.TitleAlt != "" {
			title = *notification.TitleAlt
		}
		if notification.DescriptionAlt != nil && *notification.DescriptionAlt != "" {
			description = *notification.DescriptionAlt
		}
	}

	return &NotificationView{
		ID:             notification.ID,
		Title:          title,
		Description:    description,
		ClickAction:    notification.ClickAction,
		ClickActionURL: notification.ClickActionURL,
		IsReminder:     notification.IsReminder,
		CreatedAt:      createdAt,
		ReadAt:         readAt,
		NotifType:      notification.NotifTypeID,
		NotifCategory:  notifCategoryValue,
		Origin:         notification.Origin,
		Extra:          jsonExtra,
		OrganizationID: notification.OrganizationID,
		EventID:        notification.EventID,
		EventType:      notification.EventType,
	}
}
