# auto generated by mkrctl commit id 69e4b9bdcfc5217337eb3d359f6d835dbbfaf555
environment: staging

# applicationType: deployment
applicationType: rollout
autoscaling:
  enabled: true
  maxReplicas: 4
  minReplicas: 2
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
dnsConfig:
  options:
    - name: ndots
      value: "2"
fullnameOverride: ""
image:
  pullPolicy: IfNotPresent
  repository: harbor.qontak.net/mekari-hub/mekariengineering/notif-service
  tag: master
secretName: "notif-service-staging-notif-service-chart"

# ReplicaSet history limit
revisionHistoryLimit: 3

ingress:
  enabled: true
  controller:
    annotations:
      nginx.ingress.kubernetes.io/proxy-body-size: 100m
    className: nginx
    hosts:
      - host: notif-service.qontak.net
        paths:
          - path: /
            pathType: Prefix
    tls: []
nameOverride: ""
nodeSelector: {}
podAnnotations: {}
podSecurityContext: {}
ports:
  - containerPort: 4000
    name: http
    protocol: TCP
readinessProbe:
  httpGet:
    path: /api/v1/health
    port: http
  initialDelaySeconds: 15
  timeoutSeconds: 1
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
livenessProbe:
  httpGet:
    path: /api/v1/health
    port: http
  initialDelaySeconds: 15
  timeoutSeconds: 1
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
replicaCount: 1
resources:
  limits:
    cpu: 250m
    memory: 875Mi
  requests:
    cpu: 200m
    memory: 700Mi
securityContext: {}
service:
  port: 80
  type: ClusterIP
serviceAccount:
  create: true
  annotations: {}
  name: "qontak-notif-service-staging-service-account"
tolerations: []
datadog:
  enabled: true
  host: datadog-qontak-staging-beta.datadog
deploymentStrategy:
  nginx:
    canary:
      canaryService: notif-service-staging-notif-service-chart-canary
      stableService: notif-service-staging-notif-service-chart
      trafficRouting:
        nginx:
          stableIngress: notif-service-staging-notif-service-chart
      steps:
        - setWeight: 30
        - pause: { duration: 60s }
        - setWeight: 100
# Disabled first until successfuly migrated into Kubernetes
migrationJob:
  enabled: true
  resources:
    limits:
      cpu: 250m
      memory: 875Mi
    requests:
      cpu: 200m
      memory: 700Mi
  commandArgs: "bundle exec rake db:migrate:log && bundle exec rake db:migrate:primary"
  nodeSelector: {}
  affinity: {}
  tolerations: []

pdb:
  enabled: false

affinity:
  nodeAffinity:
    # Ensure pods are scheduled on nodes labeled as 'shared'
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node
          operator: In
          values:
          - shared

nginxPlus:
  enabled: "true"
  virtualServer:
    enabled: true
    instances:
      - name: "main"
        host: "notif-service.qontak.net"
        serverSnippets: |
          client_max_body_size 50m;
        upstreams:
        - name: notif-service-staging-notif-service-chart
          service: notif-service-staging-notif-service-chart
          port: 80
          lbMethod: "least_conn"
          slowStart: "30s"
          healthCheck:
            enable: true
            path: /api/v1/health
            interval: 20s
            jitter: 3s
            fails: 3
            passes: 2
            port: 4000 #container port
            tls:
              enable: false
            statusMatch: "200"
            connect-timeout: 5s
            read-timeout: 60s
            send-timeout: 60s
        - name: notif-service-staging-notif-service-chart-canary
          service: notif-service-staging-notif-service-chart-canary
          port: 80
        routes:
        - path: /
          matches:
          - conditions:
            - header: User-Agent
              value: selenium_mekari_canary_sso
            - header: X-Mekari-Canary
              value: "1"
            action:
              pass: notif-service-staging-notif-service-chart-canary
          action:
            proxy:
              upstream: notif-service-staging-notif-service-chart
  virtualServerRoute:
    enabled: false
configmap:
  data:
    APP_ENV: "staging"
    APP_NAME: "notification-service"
    DATABASE_USERNAME: "chat_staging_broadcast_usr"
    DATABASE_PASSWORD: "uIbZY0vwQsJvtdCE3Io1Kvkg"
    DATABASE_HOST: "qontak-staging-shared-psql13.pgsql.ap-southeast-5.rds.aliyuncs.com"
    DATABASE_PORT: "5432"
    DATABASE_NAME: "qontak_staging_chat_broadcast"
    DATABASE_POOL: "10"
    DATABASE_IDLE_TIMEOUT: "1m"
    DATABASE_CONN_MAX_LIFETIME: "15m"
    DATABASE_CONN_MAX_IDLE_TIME: "5m"
    DATABASE_MAX_OPEN_CONN: "10"
    DATABASE_MAX_IDLE_CONN: "5"
    KAFKA_BROKERS: "alikafka-pre-public-intl-sg-8lb3nxaqd02-1-vpc.alikafka.aliyuncs.com:9095,alikafka-pre-public-intl-sg-8lb3nxaqd02-2-vpc.alikafka.aliyuncs.com:9095,alikafka-pre-public-intl-sg-8lb3nxaqd02-3-vpc.alikafka.aliyuncs.com:9095"
    KAFKA_SASL: "true"
    KAFKA_USERNAME: "chat"
    KAFKA_PASSWORD: "wL3t3nQ2FC8rrXQ"
    KAFKA_PREFIX: "notif-service"
    KAFKA_CERT_FILE: "certs/kafka-cert.pem"
    DD_AGENT_HOST: "localhost"
    DD_AGENT_PORT: "8126"
    STATSD_HOST: "localhost"
    STATSD_PORT: "8125"
    DATADOG_TRACER: "false"
    FCM_CRM_PROJECT_ID: "crm-project-id"
    FCM_CRM_CREDENTIALS_FILE: "/path/to/crm-credentials.json"
    FCM_CHAT_PROJECT_ID: "development-for-hub"
    FCM_CHAT_CREDENTIALS_FILE: "certs/development-for-hub-firebase-adminsdk-z2de1-e19871008d.json"
    FCM_DEFAULT_PROJECT_ID: "development-for-hub"
    FCM_DEFAULT_CREDENTIALS_FILE: "certs/development-for-hub-firebase-adminsdk-z2de1-e19871008d.json"
