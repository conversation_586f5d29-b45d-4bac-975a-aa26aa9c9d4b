{{- if and .Values.nginxPlus .Values.nginxPlus.virtualServer -}}
{{- if and .Values.nginxPlus.virtualServer.enabled (not (empty .Values.nginxPlus.virtualServer.instances)) -}}
{{- $fullName := include "notif-service-chart.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- $labels := include "notif-service-chart.labels" . -}}
{{- $selectorLabels := include "notif-service-chart.selectorLabels" . -}}
{{- range .Values.nginxPlus.virtualServer.instances }}
---
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: vs-{{ .name | default "default" }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- $labels | nindent 4 }}
    {{- with .labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  host: {{ .host }}

  {{- if .tls }}
  tls:
    {{- toYaml .tls | nindent 4 }}
  {{- end }}
  
  {{- if .upstreams }}
  upstreams:
    {{- range .upstreams }}
    - name: {{ .name | default $fullName }}
      service: {{ .service | default $fullName }}
      port: {{ .port | default $svcPort }}
      {{- if .lbMethod }}
      lb-method: {{ .lbMethod }}
      {{- end }}
      {{- if .healthCheck }}
      healthCheck:
        {{- toYaml .healthCheck | nindent 8 }}
      {{- end }}
      {{- if .slowStart }}
      slow-start: {{ .slowStart }}
      {{- end }}
      {{- if hasKey . "sessionCookie" }}
      sessionCookie:
        {{- toYaml .sessionCookie | nindent 8 }}
      {{- end }}
      {{- if .queue }}
      queue: 
        {{- toYaml .queue | nindent 8 }}
      {{- end }}
      {{- if .keepalive }}
      keepalive: {{ .keepalive }}
      {{- end }}
      {{- if .connectTimeout }}
      connect-timeout: {{ .connectTimeout }}
      {{- end }}
      {{- if .readTimeout }}
      read-timeout: {{ .readTimeout }}
      {{- end }}
      {{- if .sendTimeout }}
      send-timeout: {{ .sendTimeout }}
      {{- end }}
      {{- if .maxFails }}
      max-fails: {{ .maxFails }}
      {{- end }}
      {{- if .maxConns }}
      max-conns: {{ .maxConns }}
      {{- end }}
      {{- if .failTimeout }}
      fail-timeout: {{ .failTimeout }}
      {{- end }}
      {{- if .nextUpstream }}
      next-upstream: {{ .nextUpstream }}
      {{- end }}
      {{- if .buffering }}
      buffering: {{ .buffering }}
      {{- end }}
      {{- if .buffers }}
      buffers:
        {{- toYaml .buffers | nindent 8 }}
      {{- end }}
      {{- if .bufferSize }}
      buffer-size: {{ .bufferSize }}
      {{- end }}
      {{- if .subselector }}
      subselector:
        {{- toYaml .subselector | nindent 8 }}
      {{- end }}
      {{- if .type }}
      type: {{ .type }}
      {{- end }}
      {{- if .useClusterIP }}
      use-cluster-ip: {{ .useClusterIP }}
      {{- end }}
    {{- end }}
  {{- else }}
  upstreams:
    - name: {{ $fullName }}
      service: {{ $fullName }}
      port: {{ $svcPort }}
  {{- end }}
  
  {{- if .policies }}
  policies:
    {{- toYaml .policies | nindent 4 }}
  {{- end }}
  
  {{- if .snippets }}
  snippets:
    {{- toYaml .snippets | nindent 4 }}
  {{- end }}
  
  {{- if .routes }}
  routes:
    {{- range .routes }}
    - path: {{ .path | default "/" }}
      {{- if .policies }}
      policies:
        {{- toYaml .policies | nindent 8 }}
      {{- end }}
      {{- if .matches }}
      matches:
        {{- toYaml .matches | nindent 8 }}
      {{- end }}
      {{- if .route }}
      route: {{ .route }}
      {{- end }}
      {{- if .splits }}
      splits:
        {{- toYaml .splits | nindent 8 }}
      {{- end }}
      {{- if .action }}
      action:
        {{- if kindIs "string" .action }}
        pass: {{ .action }}
        {{- else }}
        {{- toYaml .action | nindent 8 }}
        {{- end }}
      {{- else if .action_type }}
        {{- if eq .actionType "pass" }}
        pass: {{ .upstream | default $fullName }}
        {{- else if eq .actionType "redirect" }}
        redirect:
          {{- toYaml .redirect | nindent 10 }}
        {{- else if eq .actionType "return" }}
        return:
          {{- toYaml .return | nindent 10 }}
        {{- else if eq .actionType "proxy" }}
        proxy:
          {{- toYaml .proxy | nindent 10 }}
        {{- end }}
      # {{- else }}
      # action:
      #   pass: {{ $fullName }}
      {{- end }}
      {{- if .errorPages }}
      errorPages:
        {{- toYaml .errorPages | nindent 8 }}
      {{- end }}
      {{- if hasKey . "locationSnippets" }}
      location-snippets: {{ .locationSnippets | quote }}
      {{- end }}
    {{- end }}
  {{- else }}
  routes:
    - path: /
      action:
        pass: {{ $fullName }}
  {{- end }}
  
  {{- if .dos }}
  dos: {{ .dos | quote }}
  {{- end }}
  
  {{- if .ingressClassName }}
  ingressClassName: {{ .ingressClassName }}
  {{- else if $.Values.nginxPlus.virtualServer.ingressClassName }}
  ingressClassName: {{ $.Values.nginxPlus.virtualServer.ingressClassName }}
  {{- end }}
  
  {{- if .httpSnippets }}
  http-snippets: {{ .httpSnippets | quote }}
  {{- end }}
  
  {{- if .serverSnippets }}
  server-snippets: {{ .serverSnippets | quote }}
  {{- end }}
  
  {{- if hasKey . "vsr" }}
  {{- if .vsr.enabled }}
  virtualServerRoutes:
    {{- range .vsr.routes }}
    - name: {{ .name }}
      namespace: {{ .namespace | default $.Release.Namespace }}
    {{- end }}
  {{- end }}
  {{- end }}
{{- end }}
{{- end -}}
{{- end -}}