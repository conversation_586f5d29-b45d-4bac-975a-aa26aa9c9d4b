package config

import (
	"context"
	"log/slog"
	"sync"

	"github.com/IBM/sarama"
	"bitbucket.org/terbang-ventures/notification-service/pkg/kafka"
)

// MessageHandler defines the interface for handling Kafka messages
type MessageHandler func(ctx context.Context, message []byte) error

// KafkaConsumer is a generic Kafka consumer implementation
type KafkaConsumer struct {
	client        sarama.ConsumerGroup
	handlers      map[string]MessageHandler
	topics        []string
	groupID       string
	ready         chan bool
	consumerMutex sync.Mutex
}

// NewKafkaConsumer creates a new Kafka consumer
func NewKafkaConsumer(brokers []string, groupID string, topics []string, config *sarama.Config) (*KafkaConsumer, error) {
	client, err := sarama.NewConsumerGroup(brokers, groupID, config)
	if err != nil {
		return nil, err
	}

	return &KafkaConsumer{
		client:   client,
		handlers: make(map[string]MessageHandler),
		topics:   topics,
		groupID:  groupID,
		ready:    make(chan bool),
	}, nil
}

// RegisterHandler registers a message handler for a specific topic
func (c *KafkaConsumer) RegisterHandler(topic string, handler MessageHandler) {
	c.handlers[topic] = handler
}

// Start starts consuming messages from Kafka
func (c *KafkaConsumer) Start(ctx context.Context) error {
	wg := &sync.WaitGroup{}
	wg.Add(1)
	
	go func() {
		defer wg.Done()
		for {
			// `Consume` should be called inside an infinite loop, when a
			// server-side rebalance happens, the consumer session will need to be
			// recreated to get the new claims
			if err := c.client.Consume(ctx, c.topics, c); err != nil {
				slog.ErrorContext(ctx, "Error from consumer", slog.Any("error", err))
			}
			// check if context was cancelled, signaling that the consumer should stop
			if ctx.Err() != nil {
				return
			}
			c.ready = make(chan bool)
		}
	}()

	<-c.ready // Await till the consumer has been set up
	slog.InfoContext(ctx, "Kafka consumer up and running", 
		slog.String("group_id", c.groupID),
		slog.Any("topics", c.topics),
	)

	return nil
}

// Close closes the consumer
func (c *KafkaConsumer) Close() error {
	return c.client.Close()
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (c *KafkaConsumer) Setup(sarama.ConsumerGroupSession) error {
	// Mark the consumer as ready
	close(c.ready)
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (c *KafkaConsumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages()
func (c *KafkaConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		slog.InfoContext(session.Context(), "Received message",
			slog.String("topic", message.Topic),
			slog.Int("partition", int(message.Partition)),
			slog.Int64("offset", message.Offset),
		)

		// Process the message with the appropriate handler
		if handler, ok := c.handlers[message.Topic]; ok {
			if err := handler(session.Context(), message.Value); err != nil {
				slog.ErrorContext(session.Context(), "Error processing message",
					slog.Any("error", err),
					slog.String("topic", message.Topic),
				)
			}
		}

		session.MarkMessage(message, "")
	}
	return nil
}

// InitKafkaSyncProducer initializes a Kafka sync producer
func InitKafkaSyncProducer(cfg kafka.Config) kafka.Producer {
	producer, err := kafka.NewSyncProducer(cfg)
	if err != nil {
		slog.ErrorContext(context.Background(), "error while init kafka sync producer", 
			slog.Any("error", err))
		return nil
	}

	return producer
}
