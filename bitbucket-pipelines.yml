image: golang:1.24.4  # Use the official Golang image

definitions:
  caches:
    golang: ~/.cache/go-build

## [start] Staging ArgoCD
initiateArgoCD: &initiateArgoCD
  step:
    name: Initiate CD if not exist
    image: harbor.qontak.net/mekari-hub/mekariengineering/mkrctl:latest
    runs-on:
      - "qontak.staging.alicloud"
      - "self.hosted"
    condition:
      changesets:
        includePaths:
          - "deploy-alicloud/initiate-deployment.yaml"
    script:
      - export MKRCTL_CLUSTER_LOC=alicloud
      - echo "INITIATE FROM START"
      - /app/mkrctl app initiate

stagingBuildImage: &stagingBuildImage
  step:
    name: Build Image for staging
    image: harbor.qontak.net/mekari-hub/mekariengineering/mkrctl:latest
    runs-on:
      - "qontak.staging.alicloud"
      - "self.hosted"
    script:
      - export MKRCTL_CLUSTER_LOC=alicloud
      - echo "WILL BUILD == $BITBUCKET_REPO_SLUG-staging-$BITBUCKET_TAG"
      - |
        /app/mkrctl app buildv4 \
        --deploy-name staging \
        --image-tag "$BITBUCKET_REPO_SLUG-staging-${BITBUCKET_COMMIT::7}" \
        --build-arg SSH_KEY:${CENTRAL_BUILD_SERVICE_KEY} \
        --request-cpu 1 \
        --request-memory 2Gi \
        --cache true \
        --gen-dot-env \
        --use-devops-cluster false

stagingDeployWithCanary: &stagingDeployWithCanary
  step:
    name: Deploy to Canary Staging
    deployment: Staging
    image: harbor.qontak.net/mekari-hub/mekariengineering/mkrctl:latest
    runs-on:
      - "qontak.staging.alicloud"
      - "self.hosted"
    script:
      - export MKRCTL_CLUSTER_LOC=alicloud
      - /app/mkrctl app applysecret staging
      - /app/mkrctl app syncimage staging "$BITBUCKET_REPO_SLUG-staging-${BITBUCKET_COMMIT::7}" --prune --iswait false

stagingPromoteCanary: &stagingPromoteCanary
  step:
    name: Promote Full Canary Staging
    image: harbor.qontak.net/mekari-hub/mekariengineering/mkrctl:latest
    runs-on:
      - "qontak.staging.alicloud"
      - "self.hosted"
    script:
      - export MKRCTL_CLUSTER_LOC=alicloud
      - /app/mkrctl app rollout staging --action promote

stagingUpdateConfig: &stagingUpdateConfig
  step:
    name: Sync Staging ArgoCD
    image: harbor.qontak.net/mekari-hub/mekariengineering/mkrctl:latest
    runs-on:
      - "qontak.staging.alicloud"
      - "self.hosted"
    script:
      - export MKRCTL_CLUSTER_LOC=alicloud
      - echo "update config and secret staging ..."
      - /app/mkrctl app applycfg staging
      - /app/mkrctl app applysecret staging
      - /app/mkrctl app sync staging force
## [end] Staging ArgoCD

pipelines:
  custom:
    stagingDeploy:
      - <<: *stagingBuildImage
      - <<: *stagingDeployWithCanary
      - <<: *stagingPromoteCanary

    stagingUpdateConfig:
      - <<: *stagingUpdateConfig
