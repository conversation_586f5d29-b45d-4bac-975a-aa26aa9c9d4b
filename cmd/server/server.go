package server

import (
	"context"
	"fmt"
	"log/slog"

	dbLib "bitbucket.org/mid-kelola-indonesia/go-utils/database"
	"bitbucket.org/terbang-ventures/notification-service/config"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/handler"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	"bitbucket.org/terbang-ventures/notification-service/internal/server"
	"bitbucket.org/terbang-ventures/notification-service/pkg/kafka"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

var (
	address   string
	ServerCmd = &cobra.Command{
		Use:   "server",
		Short: "Run server",
		Long:  "Run broadcast service web server",
	}
	gormDBConn    *gorm.DB
	kafkaProducer kafka.Producer
)

// Close all resources. For example: database connection, redis, kafka etc
func closeResource(kafkaProducer kafka.Producer) {
	if kafkaProducer != nil {
		if err := kafkaProducer.Close(); err != nil {
			slog.Info(fmt.Sprintf("error while close kafka producer: %v", err))
		} else {
			slog.Info("successfully close kafka producer")
		}
	}
}

// Pre-requisite before server running
// For example: db or redis connection, kafka config initialization, etc.
func serverPreRun(cmd *cobra.Command, args []string) error {
	config.Init()
	config.InitAppLogger(config.LoggerConf{
		ServiceName:        config.GetAppName(),
		ServiceEnvironment: config.GetAppEnv(),
	})

	gormDBConn = config.InitDatabase(dbLib.Config{
		Source:          config.GetDatabaseDsn(""),
		ConnMaxLifetime: config.GetDatabaseConnMaxLifetime(""),
		ConnMaxIdleTime: config.GetDatabaseConnMaxIdleTime(""),
		MaxOpenConns:    config.GetDatabaseMaxOpenConn(""),
		MaxIdleConns:    config.GetDatabaseMaxIdleConn(""),
	})

	// Try to initialize Kafka, but don't fail if it's not available
	kafkaProducer = config.InitKafkaSyncProducer(kafka.Config{
		ClientID: config.GetAppName(),
		Brokers:  config.GetKafkaBrokers(),
		Sasl:     config.GetKafkaSasl(),
		Username: config.GetKafkaSaslUsername(),
		Password: config.GetKafkaSaslPassword(),
		Prefix:   config.GetKafkaPrefix(),
		Cert:     config.GetKafkaCert(),
	})

	return nil
}

// Web server starter
// All handlers should be placed in handler directory for each domain
func serverRun(cmd *cobra.Command, args []string) error {
	ctx := context.Background()
	defer closeResource(kafkaProducer)

	// Initialize handlers
	healthHandler := handler.NewHealthHandler()

	var notificationHandler handler.NotificationHandler
	var fcmTokenHandler handler.FCMTokenHandler

	// Only initialize handlers if database is available
	if gormDBConn != nil {
		// Initialize repositories
		notificationRepo := repository.NewNotificationRepository(gormDBConn)
		notificationCategoryRepo := repository.NewNotificationCategoryRepository(gormDBConn)
		fcmTokenRepo := repository.NewFCMTokenRepository(gormDBConn)

		// Initialize services
		notificationService := service.NewNotificationService(notificationRepo, notificationCategoryRepo)
		fcmTokenService := service.NewFCMTokenService(fcmTokenRepo)

		// Initialize handlers
		notificationHandler = handler.NewNotificationHandler(notificationService)
		fcmTokenHandler = handler.NewFCMTokenHandler(fcmTokenService)
	}

	appContext := server.AppContext{
		HealthHandler:       healthHandler,
		NotificationHandler: notificationHandler,
		FCMTokenHandler:     fcmTokenHandler,
	}

	apiServer := server.NewAPIServer(address, &appContext)
	apiServer.Start(ctx)

	return nil
}

func init() {
	ServerCmd.PreRunE = serverPreRun
	ServerCmd.RunE = serverRun
	ServerCmd.PersistentFlags().StringVarP(&address, "server", "s", ":4000", "Server address (default \":4000\")")
}
