package worker

import (
	"context"
	"fmt"
	stdLog "log"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	dbLib "bitbucket.org/mid-kelola-indonesia/go-utils/database"
	"bitbucket.org/terbang-ventures/notification-service/config"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/consumer"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/factory"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	"bitbucket.org/terbang-ventures/notification-service/internal/pkg/fcm"
	"bitbucket.org/terbang-ventures/notification-service/internal/worker"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

var (
	topic         string
	consumerGroup string
	gormDBConn    *gorm.DB
	kafkaConsumer *config.KafkaConsumer
)

// WorkerCmd represents the worker command
var WorkerCmd = &cobra.Command{
	Use:   "worker",
	Short: "Start a worker process",
	Long:  `Start a worker process to consume messages from Kafka.`,
	RunE:  workerRun,
}

func init() {
	WorkerCmd.Flags().StringVarP(&topic, "topic", "t", "", "Kafka topic to consume")
	WorkerCmd.Flags().StringVarP(&consumerGroup, "group", "g", "", "Kafka consumer group")
	_ = WorkerCmd.MarkFlagRequired("topic")
	_ = WorkerCmd.MarkFlagRequired("group")
}

func workerRun(cmd *cobra.Command, args []string) error {
	// Initialize configuration
	config.Init()

	// Initialize database connection
	var err error
	gormDBConn, err = dbLib.InitGORM(dbLib.POSTGRESQL, dbLib.GORMConfig{
		Config: dbLib.Config{
			Source:          config.GetDatabaseDsn(""),
			ConnMaxLifetime: config.GetDatabaseConnMaxLifetime(""),
			ConnMaxIdleTime: config.GetDatabaseConnMaxIdleTime(""),
			MaxOpenConns:    config.GetDatabaseMaxOpenConn(""),
			MaxIdleConns:    config.GetDatabaseMaxIdleConn(""),
		},
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Create context that can be canceled
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create Kafka consumer
	kafkaConsumer, err = factory.CreateKafkaConsumer(consumerGroup, []string{topic})
	if err != nil {
		return fmt.Errorf("failed to create Kafka consumer: %w", err)
	}
	defer kafkaConsumer.Close()

	// Register handlers based on topic
	if topic == worker.FCMTopic {
		// Initialize repositories
		fcmTokenRepo := repository.NewFCMTokenRepository(gormDBConn)
		notificationRepo := repository.NewNotificationRepository(gormDBConn)
		notificationCategoryRepo := repository.NewNotificationCategoryRepository(gormDBConn)

		// Initialize FCM service
		fcmService := fcm.NewFCMService()

		// Initialize services
		notificationService := service.NewNotificationService(notificationRepo, notificationCategoryRepo)
		pushNotificationService := service.NewPushNotificationService(fcmTokenRepo, fcmService, notificationService)

		// Create FCM consumer and register handler
		fcmConsumer := consumer.NewFCMConsumer(pushNotificationService)
		kafkaConsumer.RegisterHandler(topic, fcmConsumer.HandleMessage)
	} else if topic == worker.NotificationTopic {
		// Initialize notification repository and service
		notificationRepo := repository.NewNotificationRepository(gormDBConn)
		notificationCategoryRepo := repository.NewNotificationCategoryRepository(gormDBConn)
		notificationService := service.NewNotificationService(notificationRepo, notificationCategoryRepo)

		// Create notification consumer and register handler
		notificationConsumer := consumer.NewNotificationConsumer(notificationService)
		kafkaConsumer.RegisterHandler(topic, notificationConsumer.HandleMessage)
	} else {
		stdLog.Fatalf("topic %s is not defined", topic)
	}

	// Start the consumer
	if err := kafkaConsumer.Start(ctx); err != nil {
		return fmt.Errorf("failed to start Kafka consumer: %w", err)
	}

	// Wait for termination signal
	sigterm := make(chan os.Signal, 1)
	signal.Notify(sigterm, syscall.SIGINT, syscall.SIGTERM)
	<-sigterm

	slog.Info("Terminating worker...")
	cancel()

	return nil
}
