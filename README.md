![technology:go](https://img.shields.io/badge/technology-go-blue.svg)
# Broadcast Service

A service for Broadcasting system

## How do I get set up? ###

1. Install go 1.24.4 or later
2. Prepare dependencies on your local
3. Copy .env.example to .env and then set the value as needed especially for dependencies.
    * Or you can set up vault on your local, and follow the keys from .env.example and set the VAULT_ENABLED = true
4. How to run server API:
    * `make build && ./bin/notification-service server`
    * or alternatively `make run ARGS=server`
    * see more command with `make build && ./bin/notification-service server --help`
5. How to run Worker:
    * `make build && ./bin/notification-service worker -t example-topic -g example-topic`
    * or alternatively `make run ARGS="worker -t example-topic -g example-topic"`
    * see more command with `make build && ./bin/notification-service worker --help`

## Notifications Feature

### Overview
The Notifications feature allows the service to store and manage user notifications. It provides API endpoints for creating notifications and retrieving them with pagination.

### Database Schema
The notification_banks table has the following structure:

```sql
CREATE TABLE IF NOT EXISTS notification_banks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sso_id UUID NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    title_alt TEXT,  -- Alternative title for Indonesian language
    description_alt TEXT,  -- Alternative description for Indonesian language
    click_action VARCHAR(50),
    click_action_url TEXT,
    read_at TIMESTAMP,
    is_reminder BOOLEAN DEFAULT FALSE,
    notif_type_id TEXT NOT NULL,
    notif_category_id UUID REFERENCES notification_categories(id) ON DELETE SET NULL,
    event_type VARCHAR(100),
    origin VARCHAR(50) CHECK (origin IN ('chat', 'crm')),
    extra JSONB,
    organization_id UUID,
    event_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

**Key Features:**
- **SSO Authentication**: Uses `sso_id` for SSO-based user identification
- **Internationalization**: Supports `title_alt` and `description_alt` for Indonesian translations
- **Static Notification Types**: Uses string-based `notif_type_id` with predefined types (1=General, 2=Approval, 3=Inbox)
- **Foreign Key Relations**: `notif_category_id` references `notification_categories` table
- **Origin Filtering**: `origin` field distinguishes between 'chat' and 'crm' notifications
- **Flexible Metadata**: `extra` JSONB field for additional data
- **Event Tracking**: `organization_id` and `event_id` for tracking purposes
- **Retention Policies**: Different retention periods per notification type (7 days for Inbox, 90 days for General/Approval)

### API Endpoints

#### Create Chat Notification (Internal API)
- **Method**: POST
- **URL**: `/api/v1/notifications/chat`
- **Headers**: `X-Api-Key: <internal-api-key>`
- **Request Body**:
  ```json
  {
    "sso_id": "493fc4e6-14b9-4633-95d0-86b2e27c2387",
    "title": "New Message",
    "description": "You have a new message",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/message/123",
    "is_reminder": false,
    "notif_type": "3",
    "notif_category": "category_123",
    "event_type": "add_agent",
    "extra": {},
    "organization_id": "uuid",
    "event_id": "uuid"
  }
  ```

#### Create CRM Notification (Internal API)
- **Method**: POST
- **URL**: `/api/v1/notifications/crm`
- **Headers**: `X-Api-Key: <internal-api-key>`
- **Request Body**: Same as Chat notification but with `notif_type` "1" or "2"

#### Get All Notifications (with pagination and filtering)
- **Method**: GET
- **URL**: `/api/v1/notifications`
- **Headers**: `X-Authenticated-Userid: <sso-id>`
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Number of items per page (default: 10, max: 100)
  - `origin`: Filter by origin ('chat' or 'crm')
  - `status`: Filter by read status ('read' or 'unread')
  - `notif_type`: Filter by notification type ('1', '2', or '3')
  - `notif_category`: Filter by notification category
  - `time_offset`: Time offset in hours for timezone adjustment (-14 to 14)
  - `language`: Language for title/description ('en' or 'id', default: 'en')
- **Note**: Returns notifications for the authenticated user with internationalization support

#### Get Notification by ID
- **Method**: GET
- **URL**: `/api/v1/notifications/{id}`
- **Headers**: `X-Authenticated-Userid: <sso-id>`
- **Note**: Returns notification only if it belongs to the authenticated user

#### Mark Notification as Read
- **Method**: PUT
- **URL**: `/api/v1/notifications/mark_as_read/{id}`
- **Headers**: `X-Authenticated-Userid: <sso-id>`

#### Mark All Notifications as Read
- **Method**: PUT
- **URL**: `/api/v1/notifications/mark_all_as_read`
- **Headers**: `X-Authenticated-Userid: <sso-id>`
- **Note**: Marks all notifications as read for the authenticated user

#### Count Unread Notifications
- **Method**: GET
- **URL**: `/api/v1/notifications/unread/count`
- **Headers**: `X-Authenticated-Userid: <sso-id>`
- **Response**: `{"data": {"count": 5}, "meta": {}}`
- **Note**: Returns count of unread notifications for the authenticated user

### Code Structure
The Notifications feature follows a clean architecture approach with the following components:

1. **Model** (`internal/app/model/notification.go`): Defines the Notification struct and its database mapping.

2. **Repository** (`internal/app/repository/notification_repository.go`): Handles database operations for notifications.

3. **Service** (`internal/app/service/notification_service.go`): Contains business logic for notifications.

4. **Handler** (`internal/app/handler/notification_handler.go`): Implements API endpoints for notifications.

5. **Routes** (`internal/server/route.go`): Registers notification API endpoints.

### Database Migration

This project uses a **single merged migration** for clean database setup.

**Option 1: Using the migration script (easiest)**
```bash
# Run the migration script
./scripts/migrate.sh --host localhost --user postgres --password password --database notification_service

# With custom migration table name
./scripts/migrate.sh --host localhost --user postgres --password password --database notification_service --migrations-table my_custom_migrations
```

**Option 2: Using migrate command directly**
```bash
# Create all tables from scratch with final schema
migrate -path migrations -database "postgresql://postgres:password@127.0.0.1:5432/notification_service?sslmode=disable" up 1

# With custom migration table name
migrate -path migrations -database "postgresql://postgres:password@127.0.0.1:5432/notification_service?sslmode=disable&x-migrations-table=my_custom_migrations" up 1
```

This runs the `20250712000000_create_all_tables_merged.up.sql` migration which creates:
- `notification_banks` table (main notifications table)
- `fcm_tokens` table
- `notification_types` table
- `notification_categories` table

**Note**: Historical migrations have been removed. This single migration creates the complete, optimized schema.

### Prerequisites

Before running the server, make sure the following services are running:

1. **PostgreSQL**: The database server should be running in a Docker container named `postgres`.
   ```bash
   # Check if PostgreSQL is running
   docker ps | grep postgres
   ```

2. **Kafka**: The Kafka server should be running in a Docker container named `qontak-kafka-1`.
   ```bash
   # Check if Kafka is running
   docker ps | grep qontak-kafka-1
   ```

### Testing the API

After starting the server with `go run main.go server` or `make run ARGS=server`, you can test the API endpoints using curl or any API client:

```bash
# Create a chat notification (Internal API - requires API key)
curl -X POST http://localhost:4000/api/v1/notifications/chat \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: <your-internal-api-key>" \
  -d '{
    "sso_id": "493fc4e6-14b9-4633-95d0-86b2e27c2387",
    "title": "New Message",
    "description": "You have a new message",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/message/123",
    "notif_type": "3",
    "origin": "chat"
  }'

# Get all notifications with filtering and Indonesian language
curl -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  "http://localhost:4000/api/v1/notifications?page=1&limit=10&origin=chat&status=unread&language=id"

# Get notification by ID (with SSO validation)
curl -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications/123e4567-e89b-12d3-a456-426614174000

# Mark notification as read
curl -X PUT -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications/mark_as_read/123e4567-e89b-12d3-a456-426614174000

# Mark all notifications as read
curl -X PUT -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications/mark_all_as_read

# Get count of unread notifications
curl -H "X-Authenticated-Userid: 493fc4e6-14b9-4633-95d0-86b2e27c2387" \
  http://localhost:4000/api/v1/notifications/unread/count
```

### Unit Tests

The Notifications feature includes comprehensive unit tests for all components. To run the tests:

```bash
make test
```

Test coverage for the notification components:
- **Model**: 100% coverage
- **Repository**: Tests are skipped as they require a real database connection
- **Service**: 100% coverage
- **Handler**: 94.7% coverage

#### Test Structure

1. **Model Tests** (`internal/app/model/notification_test.go`): Tests for the Notification model, including TableName method, BeforeCreate hook, and BeforeUpdate hook.

2. **Repository Tests** (`internal/app/repository/notification_repository_test.go`): Tests for the NotificationRepository, currently skipped as they require a real database connection.

3. **Service Tests** (`internal/app/service/notification_service_test.go`): Tests for the NotificationService, including tests for creating notifications, retrieving by ID, and retrieving all with pagination.

4. **Handler Tests** (`internal/app/handler/notification_handler_test.go`): Tests for the NotificationHandler, including tests for all API endpoints and various error scenarios.

#### Important Notes

- Always make sure all tests pass after making changes to the codebase.
- Update test cases whenever you add, change, or delete features.

## Recent Changes

### Major Feature Updates (2025)

The Notification Service has undergone significant enhancements:

1. **Dual Authentication System**: SSO authentication for users and API key authentication for internal services
2. **Internationalization**: Support for English and Indonesian languages with `title_alt` and `description_alt` fields
3. **Static Notification Types**: Simplified notification types (1=General, 2=Approval, 3=Inbox) with retention policies
4. **Advanced Filtering**: Filter notifications by origin, read status, type, category, and language
5. **Enhanced Security**: User isolation with SSO validation ensuring users only access their own notifications
6. **Internal API Endpoints**: Separate endpoints for Chat (`/notifications/chat`) and CRM (`/notifications/crm`) applications
7. **Timezone Support**: Time offset parameter for proper timezone handling

For detailed information about these features, see [New Features Documentation](docs/development/new_features.md).

### Rollbar Removal

Rollbar error tracking has been removed from the project as it was not being actively used. This change simplifies the project dependencies and reduces potential security vulnerabilities from unused third-party libraries.

For more details, see [Rollbar Removal Documentation](docs/changes/remove-rollbar.md).