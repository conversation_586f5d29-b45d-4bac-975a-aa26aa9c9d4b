package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Load .env file
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	// Connect to database
	db, err := connectDB()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Create repository
	categoryRepo := repository.NewNotificationCategoryRepository(db)

	// Get all notification categories
	categories, err := categoryRepo.FindAll(context.Background())
	if err != nil {
		log.Fatal("Failed to fetch notification categories:", err)
	}

	// Print results
	fmt.Println("Available Notification Categories:")
	fmt.Println("==================================")
	fmt.Printf("%-35s %-8s %-20s %-10s\n", "ID", "Origin", "Name", "Value")
	fmt.Println("-------------------------------------------------------------------------")

	for _, category := range categories {
		fmt.Printf("%-35s %-8s %-20s %-10s\n",
			category.ID.String(),
			category.Origin,
			category.Name,
			category.OriginValue)
	}

	fmt.Printf("\nTotal: %d categories\n", len(categories))
}

func connectDB() (*gorm.DB, error) {
	dbHost := os.Getenv("DATABASE_HOST")
	dbUser := os.Getenv("DATABASE_USERNAME")
	dbPassword := os.Getenv("DATABASE_PASSWORD")
	dbName := os.Getenv("DATABASE_NAME")
	dbPort := os.Getenv("DATABASE_PORT")

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		dbHost, dbUser, dbPassword, dbName, dbPort)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	return db, nil
}
